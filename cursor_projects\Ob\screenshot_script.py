import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import os

def take_full_page_screenshot():
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--window-size=1400,3000')
    
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        
        # 获取HTML文件的完整路径
        html_path = os.path.abspath("提升精力行动指南推广图.html")
        file_url = f"file:///{html_path.replace(os.sep, '/')}"
        
        print(f"正在访问: {file_url}")
        
        # 打开页面
        driver.get(file_url)
        
        # 等待页面加载
        time.sleep(3)
        
        # 获取页面总高度
        total_height = driver.execute_script("return document.body.scrollHeight")
        print(f"页面总高度: {total_height}px")
        
        # 设置窗口大小为页面实际大小
        driver.set_window_size(1400, total_height)
        
        # 再次等待
        time.sleep(2)
        
        # 截取完整页面
        screenshot_path = "提升精力行动指南推广图.jpg"
        driver.save_screenshot(screenshot_path)
        
        print(f"截图已保存到: {os.path.abspath(screenshot_path)}")
        
        # 关闭浏览器
        driver.quit()
        
        return True
        
    except Exception as e:
        print(f"截图失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = take_full_page_screenshot()
    if success:
        print("截图完成！")
    else:
        print("截图失败！")
