import os
import time
from PIL import Image, ImageDraw, ImageFont

def generate_simple_image(output_file):
    """
    使用PIL生成简单的推广图片
    
    Args:
        output_file: 输出PNG文件路径
    """
    # 创建一个1200x1000的图片，深色背景
    width, height = 1200, 1000
    image = Image.new('RGB', (width, height), (26, 29, 41))
    draw = ImageDraw.Draw(image)
    
    # 尝试加载字体，如果失败则使用默认字体
    try:
        title_font = ImageFont.truetype("arial.ttf", 48)
        subtitle_font = ImageFont.truetype("arial.ttf", 24)
        normal_font = ImageFont.truetype("arial.ttf", 18)
        small_font = ImageFont.truetype("arial.ttf", 14)
    except IOError:
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
        normal_font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # 绘制标题
    title = "Obsidian 任务管理系统"
    title_width = draw.textlength(title, font=title_font)
    draw.text(((width - title_width) / 2, 50), title, font=title_font, fill=(0, 212, 170))
    
    # 绘制副标题
    subtitle = "高效规划 · 科学执行 · 数据驱动"
    subtitle_width = draw.textlength(subtitle, font=subtitle_font)
    draw.text(((width - subtitle_width) / 2, 120), subtitle, font=subtitle_font, fill=(255, 255, 255))
    
    # 绘制描述
    description = "基于 Obsidian + Dataview 构建的全方位任务管理解决方案"
    desc_width = draw.textlength(description, font=normal_font)
    draw.text(((width - desc_width) / 2, 170), description, font=normal_font, fill=(160, 163, 177))
    
    # 绘制六个功能模块
    features = [
        ("📊 任务统计卡片", "七彩卡片直观展示任务分布状态"),
        ("🍅 番茄钟管理", "设定每日番茄钟目标，追踪实际完成情况"),
        ("📁 项目任务分布", "智能识别项目文件和待整理任务"),
        ("🎯 主子任务关系", "通过@任务名语法轻松关联主子任务"),
        ("🔍 智能筛选", "多维度任务过滤系统，支持按条件组合筛选"),
        ("📈 数据可视化", "实时生成关键指标的可视化图表")
    ]
    
    # 绘制功能模块
    col_width = width // 3
    row_height = 180
    padding = 20
    
    for i, (title, desc) in enumerate(features):
        col = i % 3
        row = i // 3
        
        x = col * col_width + padding
        y = 250 + row * row_height
        
        # 绘制模块背景
        draw.rectangle(
            [x, y, x + col_width - padding * 2, y + row_height - padding],
            fill=(40, 43, 55),
            outline=(0, 212, 170),
            width=1
        )
        
        # 绘制模块标题
        draw.text((x + 15, y + 15), title, font=subtitle_font, fill=(0, 212, 170))
        
        # 绘制模块描述
        draw.text((x + 15, y + 60), desc, font=small_font, fill=(160, 163, 177))
    
    # 绘制工作流程
    workflow = ["快速捕获", "任务整理", "专注执行", "数据分析", "持续改进"]
    flow_y = 650
    flow_width = width - 100
    step_width = flow_width // len(workflow)
    
    for i, step in enumerate(workflow):
        x = 50 + i * step_width
        
        # 绘制步骤背景
        draw.rectangle(
            [x, flow_y, x + step_width - 10, flow_y + 60],
            fill=(40, 43, 55),
            outline=(0, 212, 170),
            width=1
        )
        
        # 绘制步骤文字
        step_width_text = draw.textlength(step, font=normal_font)
        draw.text((x + (step_width - 10 - step_width_text) / 2, flow_y + 20), step, font=normal_font, fill=(255, 255, 255))
        
        # 绘制箭头（除了最后一个步骤）
        if i < len(workflow) - 1:
            draw.text((x + step_width - 5, flow_y + 20), "→", font=normal_font, fill=(0, 212, 170))
    
    # 绘制底部信息
    footer = "Obsidian 任务管理系统 | 版本 v1.0 | 基于 Dataview 插件构建"
    footer_width = draw.textlength(footer, font=small_font)
    draw.text(((width - footer_width) / 2, height - 50), footer, font=small_font, fill=(139, 142, 155))
    
    # 保存图片
    print(f"保存图片到: {output_file}")
    image.save(output_file)
    print("图片生成成功!")

if __name__ == "__main__":
    # 设置文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    output_file = os.path.join(current_dir, "obsidian_task_hexagon_promo_simple.png")
    
    # 生成推广图片
    generate_simple_image(output_file) 