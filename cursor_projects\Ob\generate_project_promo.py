import os
import sys
import time
from unified_image_generator import ImageGenerator

def generate_project_promo():
    """
    生成Obsidian项目管理系统推广图片
    """
    # 获取当前脚本目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 设置输入HTML文件和输出PNG文件路径
    html_file = os.path.join(current_dir, "obsidian_project_system_promo.html")
    output_file = os.path.join(current_dir, "obsidian_project_system_promo.png")
    
    print("=" * 60)
    print("Obsidian项目管理系统推广图片生成器")
    print("=" * 60)
    print(f"HTML模板: {html_file}")
    print(f"输出文件: {output_file}")
    print(f"图片尺寸: 1200x1800")
    print("=" * 60)
    
    # 创建图片生成器
    generator = ImageGenerator()
    
    # 生成图片
    start_time = time.time()
    result = generator.generate(
        output_file=output_file, 
        method="html", 
        input_file=html_file,
        size=(1200, 1800)  # 按照用户要求设置尺寸
    )
    
    # 显示结果
    if result:
        print("推广图片生成完成!")
        print(f"耗时: {time.time() - start_time:.2f} 秒")
        print(f"图片已保存到: {output_file}")
    else:
        print("推广图片生成失败!")

if __name__ == "__main__":
    generate_project_promo() 