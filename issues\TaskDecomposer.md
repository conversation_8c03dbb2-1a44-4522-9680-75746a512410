# TaskDecomposer - CursorRule

## 规则描述
TaskDecomposer是一个帮助将复杂任务分解为可管理步骤并追踪执行进度的CursorRule。它提供了标准化的任务分解格式和进度记录方法。

## 使用方法
使用"@TaskDecomposer [任务描述]"激活此规则。

## 任务分解格式

当开始一个新任务时，我会：

1. 将任务分解为逻辑步骤
2. 为每个步骤创建详细说明
3. 按执行顺序排列步骤
4. 跟踪每个步骤的执行进度

## 任务计划模板


## 进度追踪模板

## 示例

下面是一个使用TaskDecomposer的示例：

**用户**: @TaskDecomposer 实现一个Vue Router导航系统

**助手**:

# 任务计划：实现Vue Router导航系统
创建日期：2023-05-15

## 任务目标
为Vue应用实现一个完整的路由导航系统，支持页面间导航、路由守卫和嵌套路由。

## 任务分解
1. 安装最新版本的Vue Router
   - 预计时间：10分钟
   - 依赖项：已有Vue项目
   - 详细说明：使用npm或yarn安装最新版Vue Router，并确认安装成功
   
2. 创建路由模块结构
   - 预计时间：20分钟
   - 依赖项：步骤1
   - 详细说明：在src/router目录下创建模块化的路由结构，包括index.js和modules子目录

3. 创建基本路由配置
   - 预计时间：30分钟
   - 依赖项：步骤2
   - 详细说明：在index.js中定义基本路由配置，设置路由模式并导入路由模块

4. 定义路由模块
   - 预计时间：1小时
   - 依赖项：步骤3
   - 详细说明：为应用中的每个主要功能区域创建路由模块

5. 实现路由守卫
   - 预计时间：45分钟
   - 依赖项：步骤4
   - 详细说明：添加全局前置守卫用于认证和授权检查

6. 集成到主应用
   - 预计时间：15分钟
   - 依赖项：步骤5
   - 详细说明：在主Vue应用中注册和使用路由

7. 测试路由功能
   - 预计时间：30分钟
   - 依赖项：步骤6
   - 详细说明：测试所有路由功能，包括导航、参数传递和守卫逻辑

## 执行记录格式

当开始执行任务时，我将按照以下格式记录进度：

# 任务进度：实现Vue Router导航系统

## 执行记录

### 2023-05-15 14:00
- Step: 安装最新版本的Vue Router
- Modifications: 添加vue-router@4.3.0到项目依赖
- Change Summary: 安装最新版本的Vue Router
- Reason: 执行计划步骤1
- Blockers: None
- Status: Success

### 2023-05-15 14:15
- Step: 创建路由模块结构
- Modifications: 创建src/router/modules目录
- Change Summary: 创建路由目录结构
- Reason: 执行计划步骤2
- Blockers: None
- Status: Success

