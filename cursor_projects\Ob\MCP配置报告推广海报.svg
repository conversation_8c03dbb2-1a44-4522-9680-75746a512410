<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="1800" viewBox="0 0 1200 1800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变背景 -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0066CC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6B46C1;stop-opacity:1" />
    </linearGradient>
    
    <!-- 卡片背景 -->
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.2);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" />
    </linearGradient>
    
    <!-- 图表渐变 -->
    <linearGradient id="chartGradient" x1="0%" y1="100%" x2="0%" y2="0%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.6);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0.2);stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
    
    <!-- 模糊滤镜 -->
    <filter id="blur" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="10"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="1800" fill="url(#backgroundGradient)"/>
  
  <!-- 背景图案 -->
  <g opacity="0.1">
    <circle cx="240" cy="360" r="2" fill="white"/>
    <circle cx="960" cy="1440" r="2" fill="white"/>
    <circle cx="480" cy="1080" r="1" fill="white"/>
    <circle cx="720" cy="600" r="1.5" fill="white"/>
    <circle cx="120" cy="900" r="1" fill="white"/>
    <circle cx="1080" cy="300" r="2" fill="white"/>
  </g>
  
  <!-- 几何装饰形状 -->
  <g opacity="0.1">
    <circle cx="1100" cy="180" r="100" fill="none" stroke="white" stroke-width="2"/>
    <circle cx="100" cy="1440" r="75" fill="none" stroke="white" stroke-width="2"/>
    <rect x="1080" y="1080" width="100" height="100" rx="20" fill="none" stroke="white" stroke-width="2"/>
  </g>
  
  <!-- 标题区域 -->
  <g>
    <!-- 主标题 -->
    <text x="600" y="200" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="72" font-weight="800" filter="url(#shadow)">
      MCP完整配置
    </text>
    <text x="600" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="72" font-weight="800" filter="url(#shadow)">
      与使用报告
    </text>
    
    <!-- 副标题 -->
    <text x="600" y="340" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="32" font-weight="300" opacity="0.9">
      Model Context Protocol
    </text>
    
    <!-- 日期标签 -->
    <rect x="500" y="370" width="200" height="50" rx="25" fill="rgba(255,255,255,0.2)" filter="url(#blur)"/>
    <text x="600" y="400" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="600">
      2025-06-22
    </text>
  </g>
  
  <!-- 功能特性区域 -->
  <g>
    <!-- 实时数据处理 -->
    <rect x="80" y="500" width="1040" height="120" rx="20" fill="url(#cardGradient)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
    <circle cx="150" cy="560" r="40" fill="rgba(255,255,255,0.2)"/>
    <text x="150" y="570" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="36">⚡</text>
    <text x="230" y="570" fill="white" font-family="Arial, sans-serif" font-size="28" font-weight="600">实时数据处理</text>
    
    <!-- 多维度可视化 -->
    <rect x="80" y="660" width="1040" height="120" rx="20" fill="url(#cardGradient)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
    <circle cx="150" cy="720" r="40" fill="rgba(255,255,255,0.2)"/>
    <text x="150" y="730" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="36">📊</text>
    <text x="230" y="730" fill="white" font-family="Arial, sans-serif" font-size="28" font-weight="600">多维度可视化</text>
    
    <!-- 自动报告生成 -->
    <rect x="80" y="820" width="1040" height="120" rx="20" fill="url(#cardGradient)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
    <circle cx="150" cy="880" r="40" fill="rgba(255,255,255,0.2)"/>
    <text x="150" y="890" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="36">🤖</text>
    <text x="230" y="890" fill="white" font-family="Arial, sans-serif" font-size="28" font-weight="600">自动报告生成</text>
  </g>
  
  <!-- 仪表盘模拟区域 -->
  <g>
    <rect x="80" y="980" width="1040" height="400" rx="20" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
    
    <!-- 图表区域 -->
    <rect x="120" y="1020" width="480" height="120" rx="12" fill="rgba(255,255,255,0.1)"/>
    <rect x="700" y="1020" width="480" height="120" rx="12" fill="rgba(255,255,255,0.1)"/>
    
    <!-- 图表数据条 -->
    <rect x="216" y="1060" width="288" height="80" rx="4" fill="url(#chartGradient)"/>
    <rect x="796" y="1080" width="288" height="60" rx="4" fill="url(#chartGradient)"/>
    
    <!-- 指标区域 -->
    <g>
      <!-- 配置成功率 -->
      <rect x="120" y="1200" width="320" height="120" rx="12" fill="rgba(255,255,255,0.1)"/>
      <text x="280" y="1240" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="32" font-weight="700">98%</text>
      <text x="280" y="1270" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" opacity="0.8">配置成功率</text>
      
      <!-- MCP服务 -->
      <rect x="460" y="1200" width="320" height="120" rx="12" fill="rgba(255,255,255,0.1)"/>
      <text x="620" y="1240" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="32" font-weight="700">15+</text>
      <text x="620" y="1270" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" opacity="0.8">MCP服务</text>
      
      <!-- 实时监控 -->
      <rect x="800" y="1200" width="320" height="120" rx="12" fill="rgba(255,255,255,0.1)"/>
      <text x="960" y="1240" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="32" font-weight="700">24/7</text>
      <text x="960" y="1270" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" opacity="0.8">实时监控</text>
    </g>
  </g>
  
  <!-- 底部标签 -->
  <g>
    <rect x="400" y="1500" width="400" height="60" rx="30" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.3)" stroke-width="1" filter="url(#blur)"/>
    <text x="600" y="1540" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="600">
      专业技术文档 • 完整配置指南
    </text>
  </g>
  
  <!-- 连接线装饰 -->
  <g opacity="0.3" stroke="white" stroke-width="1" fill="none">
    <path d="M 200 560 Q 300 500 400 560"/>
    <path d="M 800 720 Q 900 660 1000 720"/>
    <path d="M 300 880 Q 400 820 500 880"/>
  </g>
</svg>
