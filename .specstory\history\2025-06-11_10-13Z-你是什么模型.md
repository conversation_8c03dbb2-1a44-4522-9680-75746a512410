<!-- Generated by SpecStory -->

# 你是什么模型 (2025-06-11 10:13Z)

_**User**_

你是什么模型

---

_**Assistant**_



---

_**User**_

你是什么模型

---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Acknowledging Identity & Instructions**

I've registered the user's inquiry regarding my identity. As per the rules, I've confirmed my nature as a large language model created by Google. The Chinese instruction for this query is now in effect, and I am ready to formulate a response in Mandarin.


**Clarifying the Answer's Format**

I'm now refining the format of my response. The user's request for a concise answer necessitates adhering to the `[模式：快速]`. My answer will be delivered in Mandarin. The tool interaction for feedback will be handled accordingly. I'm focusing on crafting a response that is both accurate and adheres to the specified workflow.


**Constructing a Concise Response**

I've formulated my response using the `[模式：快速]` workflow. I'll begin by stating the mode, then answer the question: "我是一个大型语言模型，由 Google 训练。" Afterward, I'll employ the interactive_feedback tool to seek confirmation. My goal is accuracy and conciseness, adhering to the user's instructions while delivering the answer in Mandarin. I have the `project_directory` and `summary` ready for the `interactive_feedback` tool.


</details></think>

---

[模式：快速]
我是一个大型语言模型，由 Google 训练。

---



---



---

_**User**_

shrimp-task-manager mcp介绍下MCP

---

_**Assistant**_



---

_**User**_

shrimp-task-manager mcp

---

_**Assistant**_

<details>
            <summary>Searched codebase "mcp" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `5_Structures/系统/Cursor/MCP终极指南.md` | L1-37 |
| `文档/MCP完整文档.md` | L1-66 |
| `notes/MCP工具精通学习手册.md` | L1-78 |
| `开发工具集/01_配置文件/MCP配置/mcp_config.json` | L1-33 |
| `文档/MCP工具使用规则.md` | L1-68 |
| `文档/MCP完整安装使用指南.md` | L1-91 |
| `开发工具集/02_开发工具/测试工具/setup_project_mcp.py` | L1-49 |
| `开发工具集/01_配置文件/MCP配置/mcp_backup.json` | L1-15 |
| `开发工具集/01_配置文件/MCP配置/完整MCP配置.json` | L1-31 |
| `文档/MCP完整安装使用指南.md` | L278-359 |
| `开发工具集/01_配置文件/MCP配置/修复版MCP配置.json` | L1-31 |
| `文档/MCP工具使用说明.md` | L1-68 |
| `文档/Shrimp-Task-Manager-MCP配置指南.md` | L1-80 |
| `开发工具集/02_开发工具/测试工具/setup_project_mcp.py` | L49-52 |
| `文档/MCP完整文档.md` | L66-139 |
| `issues/MCP工具使用说明.md` | L1-25 |
| `文档/README2.md` | L310-382 |
| `5_Structures/系统/Cursor/MCP终极指南.md` | L37-76 |
| `.cursor/mcp.json` | L1-36 |
| `notes/MCP工具精通学习手册.md` | L78-143 |
| `notes/MCP最佳实践与问题解决方案.md` | L1-82 |
| `文档/MCP完整安装使用指南.md` | L590-660 |
| `开发工具集/02_开发工具/内容生成器/infographic_generator.py` | L1-46 |
| `notes/MCP工具精通学习手册.md` | L143-212 |
| `5_Structures/系统/Cursor/MCP终极指南.md` | L115-152 |

</details>

---

[模式：研究]  
已检索到 shrimp-task-manager 及 MCP 相关详细资料，核心要点如下：

---

**Shrimp Task Manager（🦐 shrimp-task-manager）**  
- 是基于 MCP 协议的智能任务管理系统，专为 AI Agent 场景设计。
- 具备：智能任务规划、自动任务分解、依赖管理、研究模式、项目规则管理、Web GUI 可视化等功能。
- 支持中文模板，数据持久化，适合复杂项目的任务流管理。
- 典型用法如：任务规划、分解、执行、研究、规则初始化等。

**MCP（Model Context Protocol）**  
- 是一种 AI 工具/Agent 通用协议，类似“Type-C”标准，统一工具注册、发现、调用、参数校验、结果返回等机制。
- 通过 JSON-RPC 2.0 进行进程间通信，支持多种工具（如 shrimp-task-manager、mcp-feedback-enhanced、fetch、context7、sequential-thinking）。
- 主要优势：  
  1. 工具能力标准化，易于扩展和复用。  
  2. 支持多工具组合，提升 AI Agent 的复杂任务处理能力。  
  3. 便于上下文管理、反馈收集、实时数据获取、逻辑推理等。
- 典型场景：任务链式分解、研究驱动开发、反馈驱动优化等。

---

如需更详细的功能、配置、用法或实际案例，请指定关注点。  
是否需要进入[模式：构思]，给出多种应用/集成方案？

---

_**User**_

shrimp-task-manager mcp介绍下MCP

---

_**Assistant**_



---

