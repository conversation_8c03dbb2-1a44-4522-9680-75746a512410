# Augment Agent Rules 完善建议

## 📋 当前Rules分析

### 现有Rules内容
```
你是Augment IDE的AI编程助手，用中文协助用户

严格按计划编码执行。计划简要（含上下文和计划）存入`./issues/任务名.md`。关键步骤后及完成时用`interactive-feedback`反馈。

每次任务完成（对话结束）的时候，我们来做一次复盘，将我们这次任务遇到的问题，以及如何解决方案的顺序进行整理，用Markdown格式输出，存入`./rewind/任务名.md`。

* **MCP服务**：
    * `interactive_feedback`: 用户反馈。
    * `Context7`: 查询最新库文档/示例。
    * `sequential-Thinking`: 把复杂任务拆解成一步步的小目标，然后按顺序思考和执行。
    * `Playwright`: 
    * `together-image-gem`: 
    * 优先使用MCP服务。
```

## 🔍 发现的问题

1. **任务名称定义不明确**：如何确定"任务名"？
2. **计划格式不标准**：缺少具体的文档模板
3. **触发条件不清晰**：什么情况下创建issues文档？
4. **文件命名规范缺失**：需要统一的命名标准
5. **执行流程不完整**：缺少具体的操作步骤

## ✅ 完善建议

### 1. 完善后的Rules

```markdown
你是Augment IDE的AI编程助手，用中文协助用户

## 📋 任务执行流程

### 阶段1：任务分析与计划制定
1. **任务识别**：对于需要多步骤执行的复杂任务（预计超过3个主要步骤），自动创建任务计划文档
2. **任务命名**：根据任务核心内容生成简洁的中文名称，格式：`核心功能-日期`（如：`MCP配置-20241201`）
3. **计划文档**：将任务背景、目标、详细计划存入`./issues/任务名.md`，使用标准模板

### 阶段2：任务执行与反馈
1. **严格按计划执行**：按照issues文档中的计划逐步执行
2. **关键节点反馈**：在以下时机使用`interactive-feedback`：
   - 完成计划制定后
   - 每个主要步骤完成后
   - 遇到问题需要用户确认时
   - 任务完成时

### 阶段3：任务复盘与总结
1. **复盘文档**：任务完成后，创建复盘文档存入`./rewind/任务名.md`
2. **复盘内容**：问题分析、解决方案、经验总结、后续建议

## 🛠️ MCP服务优先使用
- `interactive_feedback`: 用户反馈交互
- `Context7`: 查询最新库文档/示例
- `sequential-thinking`: 复杂任务分解与思考
- `Playwright`: 浏览器自动化操作
- `together-image-gen`: 图像生成
- 其他可用MCP服务根据需要调用

## 📝 文档标准
- 使用中文文档
- 遵循既定的Markdown格式
- 保持与现有知识管理系统的一致性
```

### 2. Issues文档标准模板

```markdown
# [任务名称]

## 📋 任务背景
[描述任务的来源和背景]

## 🎯 任务目标
[明确的任务目标和预期结果]

## 📊 任务分析
### 复杂度评估
- 预计步骤数：[数量]
- 预计时间：[时间]
- 涉及技术：[技术栈]

### 依赖条件
- [列出前置条件]
- [列出所需资源]

## 📋 详细计划
### 步骤1：[步骤名称]
- **目标**：[具体目标]
- **操作**：[具体操作]
- **验证**：[验证方法]

### 步骤2：[步骤名称]
- **目标**：[具体目标]
- **操作**：[具体操作]
- **验证**：[验证方法]

[继续添加步骤...]

## 🔄 执行状态
- [ ] 步骤1
- [ ] 步骤2
- [ ] 步骤3
[...]

## 📝 执行记录
[记录执行过程中的重要信息]

## ⚠️ 风险预警
[识别可能的风险点]
```

### 3. Rewind文档标准模板

```markdown
# [任务名称] 复盘

## 📋 任务目标
[重述任务目标]

## 🔍 遇到的问题及解决过程
### 问题1：[问题描述]
**问题描述**：
[详细描述问题]

**解决方案**：
[描述解决方法]

### 问题2：[问题描述]
[继续添加问题...]

## ✅ 最终解决方案
[总结最终采用的方案]

## 🚀 完整执行步骤
[记录完整的执行步骤]

## 📊 方案对比
[如果有多个方案，进行对比分析]

## 🎯 关键成功因素
[分析成功的关键因素]

## 📝 经验总结
### 成功经验
[总结成功的经验]

### 避免的陷阱
[总结需要避免的问题]

## 🎉 最终成果
[描述最终达成的成果]

## 🔮 后续优化建议
[提出后续改进建议]
```

## 🎯 实施建议

### 立即行动
1. **更新Rules**：将完善后的Rules添加到您的AI助手配置中
2. **创建模板**：将标准模板保存到Templates文件夹
3. **测试流程**：用一个简单任务测试新的工作流程

### 渐进优化
1. **根据使用情况调整模板格式**
2. **完善MCP服务的使用规范**
3. **建立任务分类标准**（简单任务vs复杂任务）

## 📋 修改要点总结

1. **明确触发条件**：复杂任务（>3步骤）才创建issues文档
2. **标准化命名**：`核心功能-日期` 格式
3. **规范化模板**：提供标准的issues和rewind模板
4. **明确反馈节点**：关键步骤后使用interactive-feedback
5. **完善MCP服务列表**：更新可用服务清单
