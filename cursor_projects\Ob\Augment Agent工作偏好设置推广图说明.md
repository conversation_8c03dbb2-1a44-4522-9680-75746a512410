# Augment Agent工作偏好设置推广图说明

## 📁 生成文件
- **Augment Agent工作偏好设置推广图.html** - Bento Grid风格HTML网页
- **Augment Agent工作偏好设置推广图.jpg** - 推广图JPG（385.7 KB）

## 🎯 推广图概览

### 内容来源
基于 `[[Augment Agent工作偏好设置]]` 文档内容，这是一份详细的AI编程助手配置指南，包含了完整的工作流程、偏好设置和最佳实践。

### 设计风格
采用 **Bento Grid风格**，现代化的不规则网格布局，深色主题配合多彩卡片，专业而富有层次感。

## 📊 核心统计数据

### 顶部统计展示
- **3** 核心阶段 - 任务分析、执行反馈、复盘总结
- **5** MCP服务 - 核心工具服务数量
- **8** 偏好模块 - 完整的配置模块
- **v1.0** 当前版本 - 文档版本标识

## 🎨 Bento Grid布局设计

### 1. 核心工作流程 (大卡片)
**位置**: 左上角，占据2x2网格空间
**内容**: 
- 三阶段工作流程可视化
- 4个详细的阶段说明
- 工作流程图：任务分析 → 执行反馈 → 复盘总结
- 核心标签：任务规划、执行跟踪、经验积累、持续优化

### 2. MCP服务优先使用
**颜色主题**: 蓝色 (#3b82f6)
**内容**:
- 5个核心MCP服务详细列表
- interactive_feedback、sequential-thinking、Context7、Playwright、together-image-gen
- 功能标签：智能交互、文档查询、自动化操作

### 3. 知识重构原则
**颜色主题**: 青色 (#06b6d4)
**内容**:
- 5个核心转化原则
- 处理流程说明
- 转化目标：化繁为简、化虚为实、保持准确

### 4. Obsidian系统偏好 (宽卡片)
**颜色主题**: 紫色 (#8b5cf6)
**内容**:
- 4个管理系统：知识管理、精力管理、任务管理、项目管理
- 每个系统的详细功能描述
- 系统标签：知识管理、任务跟踪、项目管理、数据可视化

### 5. 技术实现偏好
**颜色主题**: 橙色 (#f97316)
**内容**:
- 包管理器使用规范
- 代码显示标准
- 自动化处理原则
- 技术标签：包管理器、代码规范、自动化

### 6. 沟通协作偏好
**颜色主题**: 粉色 (#ec4899)
**内容**:
- 基本协作原则
- 语言使用偏好
- 界面设计偏好
- 协作标签：中文优先、诚实透明、协作讨论

### 7. 自动化与部署
**颜色主题**: 黄色 (#eab308)
**内容**:
- 脚本执行偏好
- 部署策略
- 文本处理规范
- 自动化标签：脚本自动化、批量处理、可重复使用

### 8. 商业化考虑
**颜色主题**: 红色 (#ef4444)
**内容**:
- 模板销售策略
- 目标平台和价格区间
- 产品类型和营销重点
- 商业标签：模板销售、治愈系设计、小红书营销

## 🎨 设计特点

### 视觉层次
1. **标题区域**: 大标题 + 副标题 + 描述 + 统计数据
2. **核心内容**: 大卡片突出最重要的工作流程
3. **详细功能**: 标准卡片展示各个偏好模块
4. **宽卡片**: Obsidian系统偏好占据更多空间

### 色彩系统
- **主色调**: 青绿色 (#00ff88) - 用于标题和统计数据
- **背景色**: 深蓝灰 (#1a1d29) - 专业科技感
- **卡片色**: 8种不同主题色区分各个模块
- **文字色**: 白色主文字，灰色辅助文字

### 交互效果
- **悬停动画**: 卡片上浮和阴影变化
- **渐变边框**: 顶部彩色边框标识
- **响应式设计**: 适配不同屏幕尺寸

## 📋 内容组织策略

### 信息密度控制
- **核心工作流程**: 详细展示，占据最大空间
- **功能模块**: 精简展示，突出关键特性
- **标签系统**: 快速识别功能分类

### 阅读流程设计
1. **统计数据** → 快速了解规模
2. **核心工作流程** → 理解主要价值
3. **功能模块** → 深入了解细节
4. **标签总结** → 快速回顾要点

## 🚀 使用场景

### 技术文档
- AI助手配置指南封面
- 工作流程说明文档
- 团队协作规范展示
- 技术培训材料

### 推广营销
- 产品功能介绍
- 服务能力展示
- 客户演示材料
- 社交媒体推广

### 内部使用
- 团队onboarding材料
- 工作规范宣传
- 最佳实践分享
- 配置标准展示

## 💡 设计亮点

### 1. 信息架构清晰
- 从宏观到微观的信息层次
- 核心功能突出展示
- 详细功能分类清晰

### 2. 视觉吸引力强
- 现代化Bento Grid布局
- 丰富的色彩系统
- 专业的深色主题

### 3. 内容完整性高
- 涵盖8个主要偏好模块
- 详细的功能说明
- 实用的配置指导

### 4. 易读性优秀
- 清晰的信息层次
- 合理的视觉权重
- 直观的图标和标签

## 📊 技术规格

### 文件信息
- **尺寸**: 1400 x 2000+ 像素（全页截图）
- **格式**: JPG
- **文件大小**: 385.7 KB
- **质量**: 95% (高质量)

### 设计技术
- **布局**: CSS Grid + Flexbox
- **响应式**: 支持桌面端、平板端、移动端
- **动画**: CSS transition动画效果
- **兼容性**: 现代浏览器全支持

## 🔄 后续优化建议

### 内容扩展
1. **添加使用案例**: 每个模块的实际应用示例
2. **配置模板**: 提供可下载的配置文件
3. **视频演示**: 嵌入工作流程演示视频
4. **FAQ部分**: 常见问题和解答

### 交互增强
1. **点击展开**: 卡片点击查看详细信息
2. **搜索功能**: 快速查找特定配置
3. **导出功能**: 生成个人配置清单
4. **分享功能**: 社交媒体分享按钮

---

**创建时间**: 2025-06-23
**设计风格**: Bento Grid + 深色主题
**内容来源**: Augment Agent工作偏好设置文档
**适用场景**: 技术推广、团队培训、配置指南
