{"mcpServers": {"mcp-obsidian": {"command": "uvx", "args": ["mcp-obsidian"], "env": {"OBSIDIAN_API_KEY": "YOUR_OBSIDIAN_API_KEY_HERE", "OBSIDIAN_HOST": "https://127.0.0.1:27124"}}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced"], "env": {}}, "context7": {"command": "uvx", "args": ["context7"], "env": {}}, "playwright": {"command": "uvx", "args": ["playwright-mcp"], "env": {}}, "replicate-flux-mcp": {"command": "uvx", "args": ["replicate-flux-mcp"], "env": {"REPLICATE_API_TOKEN": "YOUR_REPLICATE_API_TOKEN_HERE"}}, "shrimp-task-manager": {"command": "uvx", "args": ["shrimp-task-manager"], "env": {}}, "sequential-thinking": {"command": "uvx", "args": ["sequential-thinking"], "env": {}}, "together-image-gen": {"command": "uvx", "args": ["together-image-gen"], "env": {"TOGETHER_API_KEY": "YOUR_TOGETHER_API_KEY_HERE"}}}}