<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obsidian 番茄钟系统 - 绿色清新风</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #a8e6cf 0%, #7fcdcd 25%, #81c784 50%, #aed581 75%, #c8e6c9 100%);
            color: #2e7d32;
            width: 1200px;
            height: 1800px;
            overflow: hidden;
            position: relative;
        }
        
        .container {
            padding: 80px 60px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            text-align: center;
            margin-bottom: 80px;
        }
        
        .main-title {
            font-size: 72px;
            font-weight: bold;
            color: #1b5e20;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(27, 94, 32, 0.2);
        }
        
        .subtitle {
            font-size: 32px;
            color: #2e7d32;
            margin-bottom: 15px;
            font-weight: 300;
        }
        
        .tagline {
            font-size: 24px;
            color: #388e3c;
            font-weight: 300;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 40px;
            flex: 1;
            margin-bottom: 60px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 40px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(129, 199, 132, 0.3);
            transition: all 0.3s ease;
            box-shadow: 0 6px 25px rgba(76, 175, 80, 0.15);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 35px rgba(76, 175, 80, 0.25);
            border-color: rgba(129, 199, 132, 0.5);
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }
        
        .feature-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #1b5e20;
        }
        
        .feature-desc {
            font-size: 18px;
            line-height: 1.6;
            color: #2e7d32;
        }
        
        .workflow {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 60px;
            margin-top: auto;
        }
        
        .workflow-item {
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .workflow-icon {
            font-size: 36px;
            margin-bottom: 10px;
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid #4caf50;
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
        }
        
        .workflow-text {
            font-size: 20px;
            color: #1b5e20;
            font-weight: 500;
        }
        
        .workflow-arrow {
            font-size: 24px;
            color: #4caf50;
            margin: 0 20px;
        }
        
        .card1 { 
            background: linear-gradient(135deg, rgba(200, 230, 201, 0.9), rgba(165, 214, 167, 0.9));
            border-left: 5px solid #4caf50;
        }
        .card2 { 
            background: linear-gradient(135deg, rgba(174, 213, 129, 0.9), rgba(139, 195, 74, 0.9));
            border-left: 5px solid #8bc34a;
        }
        .card3 { 
            background: linear-gradient(135deg, rgba(129, 199, 132, 0.9), rgba(102, 187, 106, 0.9));
            border-left: 5px solid #66bb6a;
        }
        .card4 { 
            background: linear-gradient(135deg, rgba(168, 230, 207, 0.9), rgba(127, 205, 205, 0.9));
            border-left: 5px solid #26a69a;
        }
        .card5 { 
            background: linear-gradient(135deg, rgba(178, 223, 219, 0.9), rgba(128, 203, 196, 0.9));
            border-left: 5px solid #4db6ac;
        }
        .card6 { 
            background: linear-gradient(135deg, rgba(185, 246, 202, 0.9), rgba(144, 238, 144, 0.9));
            border-left: 5px solid #81c784;
        }
        
        .card1 .feature-icon { color: #4caf50; }
        .card2 .feature-icon { color: #8bc34a; }
        .card3 .feature-icon { color: #66bb6a; }
        .card4 .feature-icon { color: #26a69a; }
        .card5 .feature-icon { color: #4db6ac; }
        .card6 .feature-icon { color: #81c784; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">Obsidian 番茄钟系统</h1>
            <p class="subtitle">高效专注 · 科学管理 · 数据驱动</p>
            <p class="tagline">基于 Obsidian + Dataview 构建的专业时间管理解决方案</p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card card1">
                <span class="feature-icon">📊</span>
                <h3 class="feature-title">专注统计卡片</h3>
                <p class="feature-desc">实时记录每日番茄钟完成情况，系统分析专注效率，让时间管理更科学，提升工作专注度。</p>
            </div>
            
            <div class="feature-card card2">
                <span class="feature-icon">🍅</span>
                <h3 class="feature-title">番茄钟管理</h3>
                <p class="feature-desc">设定专注时间和休息时间，追踪完成状态和专注质量，制定个性化专注计划。</p>
            </div>
            
            <div class="feature-card card3">
                <span class="feature-icon">📈</span>
                <h3 class="feature-title">进度可视化</h3>
                <p class="feature-desc">可视化展示专注时间分布，分析最佳专注时段，优化个人时间安排策略。</p>
            </div>
            
            <div class="feature-card card4">
                <span class="feature-icon">🎯</span>
                <h3 class="feature-title">目标达成率</h3>
                <p class="feature-desc">设定每日番茄钟目标，实时跟踪完成进度，激励持续专注，建立良好习惯。</p>
            </div>
            
            <div class="feature-card card5">
                <span class="feature-icon">🔍</span>
                <h3 class="feature-title">智能洞察</h3>
                <p class="feature-desc">深度分析专注模式，发现效率规律，提供个性化改进建议，持续优化表现。</p>
            </div>
            
            <div class="feature-card card6">
                <span class="feature-icon">📝</span>
                <h3 class="feature-title">数据可视化</h3>
                <p class="feature-desc">自动生成专注报告和统计图表，全面掌握时间管理效果，量化提升成果。</p>
            </div>
        </div>
        
        <div class="workflow">
            <div class="workflow-item">
                <div class="workflow-icon">📋</div>
                <span class="workflow-text">规划</span>
            </div>
            <span class="workflow-arrow">→</span>
            <div class="workflow-item">
                <div class="workflow-icon">⚡</div>
                <span class="workflow-text">执行</span>
            </div>
            <span class="workflow-arrow">→</span>
            <div class="workflow-item">
                <div class="workflow-icon">📊</div>
                <span class="workflow-text">追踪</span>
            </div>
            <span class="workflow-arrow">→</span>
            <div class="workflow-item">
                <div class="workflow-icon">⚙️</div>
                <span class="workflow-text">管理</span>
            </div>
        </div>
    </div>
</body>
</html>
