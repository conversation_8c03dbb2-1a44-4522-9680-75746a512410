# 🎨 推广图制作完整指南

> 详细介绍如何使用测试库系统制作高质量的推广图片

## 📋 目录

- [🎯 快速开始](#-快速开始)
- [🎨 设计风格](#-设计风格)
- [🛠️ 技术实现](#️-技术实现)
- [📐 尺寸规格](#-尺寸规格)
- [🔧 高级配置](#-高级配置)
- [💡 最佳实践](#-最佳实践)
- [🐛 故障排除](#-故障排除)

## 🎯 快速开始

### 基础使用

```bash
# 生成单张推广图
python scripts/generate-promo.py \
  --content="产品介绍" \
  --style=bento \
  --output=./output/images/

# 批量生成
python scripts/generate-promo.py \
  --batch=true \
  --input=content_list.json \
  --output=./output/images/
```

### 内容配置

```json
{
  "title": "产品标题",
  "description": "产品描述文字",
  "features": [
    "核心特性1",
    "核心特性2", 
    "核心特性3"
  ],
  "style": "bento",
  "size": [1920, 1080],
  "quality": 95
}
```

## 🎨 设计风格

### Bento Grid风格
- **特点**：现代化卡片布局，网格系统
- **适用场景**：产品介绍、功能展示
- **配色方案**：简洁现代，高对比度

### 治愈系奶茶风
- **特点**：温暖舒适，柔和色调
- **适用场景**：生活类产品、用户体验
- **配色方案**：暖色调，低饱和度

### 商务专业风
- **特点**：简洁大方，专业感强
- **适用场景**：企业服务、B2B产品
- **配色方案**：蓝色系，高可信度

## 🛠️ 技术实现

### HTML模板系统

```html
<!DOCTYPE html>
<html>
<head>
    <style>
        .container {
            width: {{width}}px;
            height: {{height}}px;
            background: {{background}};
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .title {
            font-size: {{title_size}}px;
            color: {{title_color}};
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .description {
            font-size: {{desc_size}}px;
            color: {{desc_color}};
            text-align: center;
            max-width: 80%;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">{{title}}</h1>
        <p class="description">{{description}}</p>
    </div>
</body>
</html>
```

### Python API使用

```python
from tools.content_generator import PromoGenerator

# 创建生成器
generator = PromoGenerator(
    template_dir="./templates/",
    output_dir="./output/images/"
)

# 配置内容
content = {
    "title": "新产品发布",
    "description": "革命性的功能体验",
    "features": ["特性1", "特性2", "特性3"],
    "background": "#f0f0f0",
    "title_color": "#333333"
}

# 生成图片
result = generator.generate(
    content=content,
    style="bento",
    size=(1920, 1080),
    quality=95
)

print(f"生成成功: {result.output_path}")
```

## 📐 尺寸规格

### 社交媒体规格

| 平台 | 尺寸 | 比例 | 用途 |
|:---:|:---:|:---:|:---:|
| 微信朋友圈 | 1200x630 | 1.91:1 | 分享卡片 |
| 微博 | 1080x1080 | 1:1 | 正方形图片 |
| 小红书 | 1080x1350 | 4:5 | 竖版图片 |
| 抖音 | 1080x1920 | 9:16 | 竖屏视频封面 |

### 网页规格

| 用途 | 尺寸 | 说明 |
|:---:|:---:|:---:|
| 横幅广告 | 1920x1080 | 网页头部横幅 |
| 文章配图 | 1200x800 | 文章内容配图 |
| 缩略图 | 400x300 | 列表缩略图 |
| 背景图 | 2560x1440 | 网页背景 |

## 🔧 高级配置

### 自定义模板

```python
# 创建自定义模板
template_config = {
    "name": "custom_template",
    "html_file": "templates/custom.html",
    "css_file": "templates/custom.css",
    "variables": {
        "primary_color": "#007bff",
        "secondary_color": "#6c757d",
        "font_family": "Arial, sans-serif"
    }
}

generator.add_template(template_config)
```

### 批量处理配置

```json
{
  "batch_config": {
    "parallel": true,
    "max_workers": 4,
    "timeout": 30,
    "retry_count": 3
  },
  "output_config": {
    "format": "jpg",
    "quality": 95,
    "optimize": true,
    "progressive": true
  },
  "content_list": [
    {
      "title": "产品A",
      "description": "产品A描述",
      "output_name": "product_a.jpg"
    }
  ]
}
```

## 💡 最佳实践

### 设计原则

1. **简洁明了**：避免信息过载
2. **层次清晰**：重要信息突出显示
3. **色彩协调**：使用一致的配色方案
4. **字体可读**：选择清晰易读的字体
5. **留白适当**：给内容足够的呼吸空间

### 内容建议

1. **标题精炼**：控制在10个字以内
2. **描述简洁**：1-2句话说明核心价值
3. **特性突出**：最多展示3-5个核心特性
4. **视觉焦点**：确保有明确的视觉重点

### 技术优化

1. **图片压缩**：在质量和文件大小间平衡
2. **格式选择**：JPG适合照片，PNG适合图标
3. **尺寸适配**：根据使用场景选择合适尺寸
4. **批量处理**：提高大量图片的生成效率

## 🐛 故障排除

### 常见问题

#### Q: 生成的图片模糊？
**A:** 检查以下设置：
- 确保输出尺寸足够大
- 提高quality参数（建议95以上）
- 检查字体大小是否合适

#### Q: 中文字体显示异常？
**A:** 解决方案：
- 安装中文字体到系统
- 在CSS中指定中文字体
- 使用Web字体或字体文件

#### Q: 批量生成失败？
**A:** 检查项目：
- 验证输入数据格式
- 检查模板文件是否存在
- 确认输出目录有写入权限

#### Q: 生成速度慢？
**A:** 优化建议：
- 启用并行处理
- 减少图片尺寸
- 优化模板复杂度
- 使用SSD存储

### 调试技巧

```python
# 启用调试模式
generator = PromoGenerator(debug=True)

# 查看详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 测试模板渲染
result = generator.test_template("bento", test_data)
print(result.html_content)
```

---

## 📚 相关资源

- [HTML模板开发文档](template-development.md)
- [CSS样式指南](css-style-guide.md)
- [图片优化指南](image-optimization.md)
- [批量处理教程](batch-processing.md)

---

*让推广图制作变得简单高效 🎨*
