<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obsidian 番茄钟系统 - AI风格</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            color: white;
            width: 1200px;
            height: 1800px;
            overflow: hidden;
            position: relative;
        }
        
        .container {
            padding: 80px 60px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            text-align: center;
            margin-bottom: 80px;
        }
        
        .main-title {
            font-size: 64px;
            font-weight: bold;
            color: #06b6d4;
            margin-bottom: 15px;
            letter-spacing: 2px;
        }
        
        .subtitle {
            font-size: 24px;
            color: #94a3b8;
            margin-bottom: 40px;
            font-weight: 300;
            letter-spacing: 1px;
            text-transform: uppercase;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            flex: 1;
        }
        
        .feature-card {
            background: rgba(30, 41, 59, 0.8);
            border-radius: 16px;
            padding: 40px;
            border: 1px solid rgba(148, 163, 184, 0.2);
            position: relative;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            border-color: rgba(6, 182, 212, 0.5);
            box-shadow: 0 20px 40px rgba(6, 182, 212, 0.1);
        }
        
        .feature-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .feature-icon {
            font-size: 32px;
            margin-right: 15px;
            width: 60px;
            height: 60px;
            background: rgba(6, 182, 212, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .feature-label {
            font-size: 16px;
            color: #06b6d4;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .feature-title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #ffffff;
        }
        
        .feature-desc {
            font-size: 16px;
            line-height: 1.6;
            color: #cbd5e1;
            margin-bottom: 20px;
        }
        
        .feature-highlight {
            font-size: 14px;
            color: #64748b;
            font-style: italic;
            border-left: 3px solid #06b6d4;
            padding-left: 15px;
            margin-top: 20px;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(148, 163, 184, 0.2);
            border-radius: 2px;
            margin-top: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #06b6d4, #0891b2);
            border-radius: 2px;
            animation: progress 2s ease-in-out;
        }
        
        @keyframes progress {
            from { width: 0%; }
            to { width: var(--progress); }
        }
        
        .card1 { 
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(30, 41, 59, 0.9));
        }
        .card1 .progress-fill { --progress: 95%; }
        
        .card2 { 
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(30, 41, 59, 0.9));
        }
        .card2 .feature-icon { background: rgba(34, 197, 94, 0.2); }
        .card2 .feature-label { color: #22c55e; }
        .card2 .feature-highlight { border-left-color: #22c55e; }
        .card2 .progress-fill { 
            background: linear-gradient(90deg, #22c55e, #16a34a);
            --progress: 88%; 
        }
        
        .card3 { 
            background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(30, 41, 59, 0.9));
        }
        .card3 .feature-icon { background: rgba(168, 85, 247, 0.2); }
        .card3 .feature-label { color: #a855f7; }
        .card3 .feature-highlight { border-left-color: #a855f7; }
        .card3 .progress-fill { 
            background: linear-gradient(90deg, #a855f7, #9333ea);
            --progress: 92%; 
        }
        
        .card4 { 
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(30, 41, 59, 0.9));
        }
        .card4 .feature-icon { background: rgba(239, 68, 68, 0.2); }
        .card4 .feature-label { color: #ef4444; }
        .card4 .feature-highlight { border-left-color: #ef4444; }
        .card4 .progress-fill { 
            background: linear-gradient(90deg, #ef4444, #dc2626);
            --progress: 85%; 
        }
        
        .card5 { 
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(30, 41, 59, 0.9));
            grid-column: 1 / -1;
            display: flex;
            align-items: center;
            padding: 30px 40px;
        }
        .card5 .feature-icon { 
            background: rgba(245, 158, 11, 0.2);
            margin-right: 30px;
        }
        .card5 .feature-label { color: #f59e0b; }
        .card5 .feature-title { 
            font-size: 28px;
            margin-bottom: 10px;
        }
        .card5 .feature-desc { 
            margin-bottom: 0;
            font-size: 18px;
        }
        .card5 .progress-fill { 
            background: linear-gradient(90deg, #f59e0b, #d97706);
            --progress: 90%; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">番茄钟效率分析</h1>
            <p class="subtitle">POMODORO PRODUCTIVITY ANALYSIS</p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card card1">
                <div class="feature-header">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-label">主动强</div>
                </div>
                <h3 class="feature-title">专注统计</h3>
                <p class="feature-desc">主要优势：主动追踪，善于使用工具，行动力强。潜在缺点：有时候可能做得太多。</p>
                <p class="feature-desc">定位：被广泛认为是能力最强的模块。</p>
                <div class="feature-highlight">"3.7作者常说主动，我才意识到一个手不释卷的模块..."</div>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
            
            <div class="feature-card card2">
                <div class="feature-header">
                    <div class="feature-icon">📊</div>
                    <div class="feature-label">结构优</div>
                </div>
                <h3 class="feature-title">数据分析</h3>
                <p class="feature-desc">最适合：调试、工具使用和透明明确的重构任务。</p>
                <p class="feature-desc">特点：很少做错主要，可靠地保持任明确特点。</p>
                <div class="feature-highlight">"其代码质量看起来很好，但更加一致。"</div>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
            
            <div class="feature-card card3">
                <div class="feature-header">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-label">专注稳</div>
                </div>
                <h3 class="feature-title">效率提升</h3>
                <p class="feature-desc">最适合：简单、需要速度的可引导性的场景。</p>
                <p class="feature-desc">特点：比其他模块更快，可靠地保持任明确特点。</p>
                <div class="feature-highlight">"它让我的编辑器更更快速，总是能够应对正确的任务。"</div>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
            
            <div class="feature-card card4">
                <div class="feature-header">
                    <div class="feature-icon">🔥</div>
                    <div class="feature-label">快速轻</div>
                </div>
                <h3 class="feature-title">时间管理</h3>
                <p class="feature-desc">最适合：快速、低复杂度任务。</p>
                <p class="feature-desc">特点：速度快，适合不需要深度理解的小型任务，而且免费。</p>
                <div class="feature-highlight">"它速度快，对于小事情很好用完美无缺。"</div>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
            
            <div class="feature-card card5">
                <div class="feature-header">
                    <div class="feature-icon">🍅</div>
                    <div class="feature-label">智能化</div>
                </div>
                <div>
                    <h3 class="feature-title">Obsidian 番茄钟系统</h3>
                    <p class="feature-desc">基于 Obsidian + Dataview 构建的专业时间管理解决方案，提供全方位的专注力分析和效率优化。</p>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
