#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP配置管理脚本
用于管理和切换不同的MCP配置
"""

import json
import shutil
from pathlib import Path

def list_configs():
    """列出所有可用的配置"""
    config_dir = Path("config/mcp")
    print("可用的MCP配置:")
    
    for ide_dir in config_dir.iterdir():
        if ide_dir.is_dir():
            print(f"\n{ide_dir.name.upper()}:")
            for config_file in ide_dir.glob("*.json"):
                print(f"  - {config_file.name}")

def copy_config(source_config, target_path):
    """复制配置文件到指定位置"""
    try:
        shutil.copy2(source_config, target_path)
        print(f"配置已复制到: {target_path}")
        return True
    except Exception as e:
        print(f"复制失败: {e}")
        return False

if __name__ == "__main__":
    list_configs()
