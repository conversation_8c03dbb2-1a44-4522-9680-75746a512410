<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提升精力的行动指南-简化版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: #1a1d29;
            color: #ffffff;
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 60px 40px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 80px;
        }
        
        .main-title {
            font-size: 3.5rem;
            font-weight: 700;
            color: #00ff88;
            margin-bottom: 15px;
            letter-spacing: -1px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #8892b0;
            margin-bottom: 20px;
            font-weight: 400;
        }
        
        .description {
            font-size: 1rem;
            color: #64748b;
            margin-bottom: 50px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .stats-section {
            display: flex;
            justify-content: center;
            gap: 80px;
            margin-bottom: 80px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #00ff88;
            margin-bottom: 8px;
            display: block;
        }
        
        .stat-label {
            font-size: 1rem;
            color: #8892b0;
            font-weight: 500;
        }
        
        .bento-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            grid-auto-rows: minmax(300px, auto);
        }
        
        .bento-card {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 20px;
            padding: 35px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .bento-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color);
            border-radius: 20px 20px 0 0;
        }
        
        .bento-card:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.05);
            border-color: var(--card-color);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .card-wide {
            grid-column: span 2;
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .card-icon {
            font-size: 2.5rem;
            margin-right: 15px;
            color: var(--card-color);
        }
        
        .card-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #ffffff;
        }
        
        .card-subtitle {
            font-size: 0.9rem;
            color: #8892b0;
            margin-top: 5px;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        
        .action-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .action-item:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: translateY(-2px);
        }
        
        .action-title {
            font-weight: 600;
            color: var(--card-color);
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .action-desc {
            font-size: 0.8rem;
            color: #a0a0a0;
        }
        
        .features-list {
            list-style: none;
            margin-bottom: 20px;
        }
        
        .features-list li {
            padding: 8px 0;
            color: #cbd5e1;
            font-size: 0.9rem;
            position: relative;
            padding-left: 20px;
        }
        
        .features-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--card-color);
            font-weight: 600;
        }
        
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .tag {
            background: rgba(255, 255, 255, 0.1);
            color: var(--card-color);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .highlight-box {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 12px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .highlight-title {
            font-weight: 600;
            color: #00ff88;
            margin-bottom: 5px;
        }
        
        .highlight-desc {
            font-size: 0.9rem;
            color: #cbd5e1;
        }
        
        /* 卡片颜色主题 */
        .card-nutrition { --card-color: #3b82f6; }
        .card-exercise { --card-color: #06b6d4; }
        .card-sleep { --card-color: #8b5cf6; }
        .card-mindset { --card-color: #f97316; }
        .card-supplements { --card-color: #ec4899; }
        
        @media (max-width: 1024px) {
            .bento-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
            }
            
            .card-wide {
                grid-column: span 1;
            }
            
            .stats-section {
                gap: 50px;
            }
        }
        
        @media (max-width: 768px) {
            .bento-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .stats-section {
                gap: 30px;
            }
            
            .main-title {
                font-size: 2.5rem;
            }
            
            .container {
                padding: 40px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">提升精力的行动指南</h1>
            <p class="subtitle">简化版 · 五大核心模块 · 立即可执行</p>
            <p class="description">聚焦最实用的精力提升方法 • 饮食 • 运动 • 睡眠 • 心态 • 补充剂 • 构建高效精力管理体系</p>
            
            <div class="stats-section">
                <div class="stat-item">
                    <span class="stat-number">5</span>
                    <span class="stat-label">核心模块</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">20+</span>
                    <span class="stat-label">实用方法</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24</span>
                    <span class="stat-label">小时管理</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">100%</span>
                    <span class="stat-label">可执行</span>
                </div>
            </div>
        </div>
        
        <div class="bento-grid">
            <!-- 饮食篇 -->
            <div class="bento-card card-nutrition">
                <div class="card-header">
                    <div class="card-icon">🥗</div>
                    <div>
                        <div class="card-title">饮食篇：为身体补充燃料</div>
                        <div class="card-subtitle">脾胃是精力的源头</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li>养胃基础：馒头、面条、白粥、小白菜</li>
                    <li>核心食疗：陈皮5g + 山药干5片</li>
                    <li>三餐分配：早6分饱、中8分饱、晚5分饱</li>
                    <li>绝对禁区：生冷粘腻、刺激性食物</li>
                    <li>进阶配方：加苹果促消化，加茯苓祛湿气</li>
                </ul>
                
                <div style="margin-top: 15px; padding: 15px; background: rgba(59, 130, 246, 0.1); border-radius: 8px;">
                    <div style="font-size: 0.9rem; color: #3b82f6; font-weight: 600;">核心配方原理</div>
                    <div style="font-size: 0.8rem; color: #a0a0a0; margin-top: 5px;">
                        陈皮理气（让能量流动）+ 山药固本（补充基础能量）
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">养胃基础</span>
                    <span class="tag">食疗配方</span>
                    <span class="tag">三餐分配</span>
                </div>
            </div>
            
            <!-- 运动篇 -->
            <div class="bento-card card-exercise">
                <div class="card-header">
                    <div class="card-icon">🏃</div>
                    <div>
                        <div class="card-title">运动篇：精力的催化剂</div>
                        <div class="card-subtitle">低消耗高回报的运动</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li>超慢跑：心率110-120次/分钟，微微出汗</li>
                    <li>强度标准：能轻松说话，12分钟/公里配速</li>
                    <li>时间频率：20-40分钟，跑一天休一天</li>
                    <li>八段锦：第3周后配合，温和不伤气血</li>
                    <li>最佳时间：下午5-7点，避免早起空腹</li>
                </ul>
                
                <div style="margin-top: 15px; padding: 15px; background: rgba(6, 182, 212, 0.1); border-radius: 8px;">
                    <div style="font-size: 0.9rem; color: #06b6d4; font-weight: 600;">超量恢复原理</div>
                    <div style="font-size: 0.8rem; color: #a0a0a0; margin-top: 5px;">
                        在身体能承受的极低阈值内运动，实现精力提升而非消耗
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">超慢跑</span>
                    <span class="tag">八段锦</span>
                    <span class="tag">超量恢复</span>
                </div>
            </div>
            
            <!-- 睡眠篇 -->
            <div class="bento-card card-sleep">
                <div class="card-header">
                    <div class="card-icon">😴</div>
                    <div>
                        <div class="card-title">睡眠篇：身体的充电站</div>
                        <div class="card-subtitle">高质量睡眠是恢复核心</div>
                    </div>
                </div>
                
                <div class="action-grid">
                    <div class="action-item">
                        <div class="action-title">环境优化</div>
                        <div class="action-desc">18-22°C、遮光窗帘、保持安静</div>
                    </div>
                    <div class="action-item">
                        <div class="action-title">睡前程序</div>
                        <div class="action-desc">关闭电子设备、调暗灯光</div>
                    </div>
                    <div class="action-item">
                        <div class="action-title">规律作息</div>
                        <div class="action-desc">同一时间睡觉起床，周末不例外</div>
                    </div>
                    <div class="action-item">
                        <div class="action-title">泡脚放松</div>
                        <div class="action-desc">40°C温水泡脚10分钟</div>
                    </div>
                </div>
                
                <div style="margin-top: 15px; padding: 15px; background: rgba(139, 92, 246, 0.1); border-radius: 8px;">
                    <div style="font-size: 0.9rem; color: #8b5cf6; font-weight: 600;">睡前1小时程序</div>
                    <div style="font-size: 0.8rem; color: #a0a0a0; margin-top: 5px;">
                        关屏幕 → 调暗灯 → 泡脚 → 规律作息
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">环境优化</span>
                    <span class="tag">睡前仪式</span>
                    <span class="tag">规律作息</span>
                </div>
            </div>
            
            <!-- 心态篇 -->
            <div class="bento-card card-mindset">
                <div class="card-header">
                    <div class="card-icon">😊</div>
                    <div>
                        <div class="card-title">心态篇：精力的根本动力</div>
                        <div class="card-subtitle">管理情绪和压力</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li>减少信息输入：少刷短视频和社交媒体</li>
                    <li>关注当下：专注自身成长，减少比较</li>
                    <li>学会放过自己：识别压力源，改变心态</li>
                    <li>练习冥想：从5分钟开始，培养内观能力</li>
                    <li>情绪调节：写日记记录，与朋友分享感受</li>
                </ul>
                
                <div style="margin-top: 15px; padding: 15px; background: rgba(249, 115, 22, 0.1); border-radius: 8px;">
                    <div style="font-size: 0.9rem; color: #f97316; font-weight: 600;">冥想进阶路径</div>
                    <div style="font-size: 0.8rem; color: #a0a0a0; margin-top: 5px;">
                        5分钟开始 → 逐渐增加到20分钟 → 培养内观能力
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">减少内耗</span>
                    <span class="tag">冥想练习</span>
                    <span class="tag">压力管理</span>
                </div>
            </div>
            
            <!-- 补充剂篇 - 宽卡片 -->
            <div class="bento-card card-wide card-supplements">
                <div class="card-header">
                    <div class="card-icon">💊</div>
                    <div>
                        <div class="card-title">补充剂篇：科学补充，事半功倍</div>
                        <div class="card-subtitle">核心补充：镁元素</div>
                    </div>
                </div>
                
                <div class="action-grid">
                    <div class="action-item">
                        <div class="action-title">为什么需要镁？</div>
                        <div class="action-desc">参与300+化学反应，现代饮食普遍缺乏</div>
                    </div>
                    <div class="action-item">
                        <div class="action-title">缺镁症状</div>
                        <div class="action-desc">肌肉抽筋、眼皮跳、疲劳、焦虑、失眠</div>
                    </div>
                    <div class="action-item">
                        <div class="action-title">食物来源（首选）</div>
                        <div class="action-desc">南瓜子、葵瓜子、杏仁、菠菜、黑巧克力</div>
                    </div>
                    <div class="action-item">
                        <div class="action-title">推荐剂量</div>
                        <div class="action-desc">男性400-420mg/日，女性310-320mg/日</div>
                    </div>
                    <div class="action-item">
                        <div class="action-title">最佳时间</div>
                        <div class="action-desc">晚上随餐或睡前服用，帮助睡眠</div>
                    </div>
                    <div class="action-item">
                        <div class="action-title">补充剂形式</div>
                        <div class="action-desc">甘氨酸镁、柠檬酸镁吸收率较高</div>
                    </div>
                </div>
                
                <div style="margin-top: 15px; padding: 15px; background: rgba(236, 72, 153, 0.1); border-radius: 8px; border-left: 3px solid #ec4899;">
                    <strong>重要提醒:</strong> 在使用补充剂前，务必咨询医生或营养师，特别是肾脏有问题的人群
                </div>
                
                <div class="tags">
                    <span class="tag">镁补充</span>
                    <span class="tag">食物来源</span>
                    <span class="tag">科学剂量</span>
                    <span class="tag">睡前服用</span>
                    <span class="tag">专业咨询</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
