<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obsidian 番茄钟系统 - 紫色梦幻风</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            color: white;
            width: 1200px;
            height: 1800px;
            overflow: hidden;
            position: relative;
        }
        
        .container {
            padding: 80px 60px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            text-align: center;
            margin-bottom: 80px;
        }
        
        .main-title {
            font-size: 72px;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
        }
        
        .subtitle {
            font-size: 32px;
            color: #ffffff;
            margin-bottom: 15px;
            font-weight: 300;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .tagline {
            font-size: 24px;
            color: #f8f9ff;
            font-weight: 300;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 40px;
            flex: 1;
            margin-bottom: 60px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 40px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }
        
        .feature-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ffffff;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
        
        .feature-desc {
            font-size: 18px;
            line-height: 1.6;
            color: #f0f0ff;
        }
        
        .workflow {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 60px;
            margin-top: auto;
        }
        
        .workflow-item {
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .workflow-icon {
            font-size: 36px;
            margin-bottom: 10px;
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        .workflow-text {
            font-size: 20px;
            color: #ffffff;
            font-weight: 500;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
        
        .workflow-arrow {
            font-size: 24px;
            color: #ffffff;
            margin: 0 20px;
        }
        
        .card1 { 
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(78, 205, 196, 0.2));
            border: 1px solid rgba(255, 107, 107, 0.3);
        }
        .card2 { 
            background: linear-gradient(135deg, rgba(69, 183, 209, 0.2), rgba(150, 206, 180, 0.2));
            border: 1px solid rgba(69, 183, 209, 0.3);
        }
        .card3 { 
            background: linear-gradient(135deg, rgba(255, 234, 167, 0.2), rgba(250, 177, 160, 0.2));
            border: 1px solid rgba(255, 234, 167, 0.3);
        }
        .card4 { 
            background: linear-gradient(135deg, rgba(162, 155, 254, 0.2), rgba(199, 146, 234, 0.2));
            border: 1px solid rgba(162, 155, 254, 0.3);
        }
        .card5 { 
            background: linear-gradient(135deg, rgba(255, 154, 158, 0.2), rgba(250, 208, 196, 0.2));
            border: 1px solid rgba(255, 154, 158, 0.3);
        }
        .card6 { 
            background: linear-gradient(135deg, rgba(79, 172, 254, 0.2), rgba(0, 242, 254, 0.2));
            border: 1px solid rgba(79, 172, 254, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">Obsidian 番茄钟系统</h1>
            <p class="subtitle">高效专注 · 科学管理 · 数据驱动</p>
            <p class="tagline">基于 Obsidian + Dataview 构建的专业时间管理解决方案</p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card card1">
                <span class="feature-icon">📊</span>
                <h3 class="feature-title">专注统计卡片</h3>
                <p class="feature-desc">实时记录每日番茄钟完成情况，系统分析专注效率，让时间管理更科学，提升工作专注度。</p>
            </div>
            
            <div class="feature-card card2">
                <span class="feature-icon">🍅</span>
                <h3 class="feature-title">番茄钟管理</h3>
                <p class="feature-desc">设定专注时间和休息时间，追踪完成状态和专注质量，制定个性化专注计划。</p>
            </div>
            
            <div class="feature-card card3">
                <span class="feature-icon">📈</span>
                <h3 class="feature-title">进度可视化</h3>
                <p class="feature-desc">可视化展示专注时间分布，分析最佳专注时段，优化个人时间安排策略。</p>
            </div>
            
            <div class="feature-card card4">
                <span class="feature-icon">🎯</span>
                <h3 class="feature-title">目标达成率</h3>
                <p class="feature-desc">设定每日番茄钟目标，实时跟踪完成进度，激励持续专注，建立良好习惯。</p>
            </div>
            
            <div class="feature-card card5">
                <span class="feature-icon">🔍</span>
                <h3 class="feature-title">智能洞察</h3>
                <p class="feature-desc">深度分析专注模式，发现效率规律，提供个性化改进建议，持续优化表现。</p>
            </div>
            
            <div class="feature-card card6">
                <span class="feature-icon">📝</span>
                <h3 class="feature-title">数据可视化</h3>
                <p class="feature-desc">自动生成专注报告和统计图表，全面掌握时间管理效果，量化提升成果。</p>
            </div>
        </div>
        
        <div class="workflow">
            <div class="workflow-item">
                <div class="workflow-icon">📋</div>
                <span class="workflow-text">规划</span>
            </div>
            <span class="workflow-arrow">→</span>
            <div class="workflow-item">
                <div class="workflow-icon">⚡</div>
                <span class="workflow-text">执行</span>
            </div>
            <span class="workflow-arrow">→</span>
            <div class="workflow-item">
                <div class="workflow-icon">📊</div>
                <span class="workflow-text">追踪</span>
            </div>
            <span class="workflow-arrow">→</span>
            <div class="workflow-item">
                <div class="workflow-icon">⚙️</div>
                <span class="workflow-text">管理</span>
            </div>
        </div>
    </div>
</body>
</html>
