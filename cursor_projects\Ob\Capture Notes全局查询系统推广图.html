<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Capture Notes全局查询系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: #1a1d29;
            color: #ffffff;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 60px 40px;
        }

        .header {
            text-align: center;
            margin-bottom: 80px;
        }

        .main-title {
            font-size: 3.5rem;
            font-weight: 700;
            color: #00ff88;
            margin-bottom: 15px;
            letter-spacing: -1px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #8892b0;
            margin-bottom: 20px;
            font-weight: 400;
        }

        .description {
            font-size: 1rem;
            color: #64748b;
            margin-bottom: 50px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .stats-section {
            display: flex;
            justify-content: center;
            gap: 80px;
            margin-bottom: 80px;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #00ff88;
            margin-bottom: 8px;
            display: block;
        }

        .stat-label {
            font-size: 1rem;
            color: #8892b0;
            font-weight: 500;
        }

        .bento-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
            grid-auto-rows: minmax(280px, auto);
        }

        .bento-card {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 20px;
            padding: 35px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .bento-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color);
            border-radius: 20px 20px 0 0;
        }

        .bento-card:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.05);
            border-color: var(--card-color);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .card-large {
            grid-column: span 2;
        }

        .card-wide {
            grid-column: span 2;
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .card-icon {
            font-size: 2.5rem;
            margin-right: 15px;
            color: var(--card-color);
        }

        .card-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #ffffff;
        }

        .card-subtitle {
            font-size: 0.9rem;
            color: #8892b0;
            margin-top: 5px;
        }

        .features-list {
            list-style: none;
            margin-bottom: 20px;
        }

        .features-list li {
            padding: 8px 0;
            color: #cbd5e1;
            font-size: 0.9rem;
            position: relative;
            padding-left: 20px;
        }

        .features-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--card-color);
            font-weight: 600;
        }

        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag {
            background: rgba(255, 255, 255, 0.1);
            color: var(--card-color);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .highlight-box {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 12px;
            padding: 15px;
            margin-top: 15px;
        }

        .highlight-title {
            font-weight: 600;
            color: #00ff88;
            margin-bottom: 5px;
        }

        .highlight-desc {
            font-size: 0.9rem;
            color: #cbd5e1;
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .action-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .action-item:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: translateY(-2px);
        }

        .action-title {
            font-weight: 600;
            color: var(--card-color);
            margin-bottom: 5px;
            font-size: 0.9rem;
        }

        .action-desc {
            font-size: 0.8rem;
            color: #a0a0a0;
        }

        /* 卡片颜色主题 */
        .card-overview { --card-color: #00ff88; }
        .card-search { --card-color: #3b82f6; }
        .card-filter { --card-color: #06b6d4; }
        .card-scroll { --card-color: #8b5cf6; }
        .card-stats { --card-color: #f97316; }

        @media (max-width: 1024px) {
            .bento-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
            }

            .card-large,
            .card-wide {
                grid-column: span 1;
            }

            .stats-section {
                gap: 50px;
            }
        }

        @media (max-width: 768px) {
            .bento-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .stats-section {
                gap: 30px;
            }

            .main-title {
                font-size: 2.5rem;
            }

            .container {
                padding: 40px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">Capture Notes全局查询系统</h1>
            <p class="subtitle">告别翻找笔记的焦虑！Obsidian知识管理神器</p>
            <p class="description">基于Dataview API构建的智能知识管理解决方案 • 3秒精准定位 • 滚动查看大数据 • 可视化统计分析</p>

            <div class="stats-section">
                <div class="stat-item">
                    <span class="stat-number">4</span>
                    <span class="stat-label">核心功能</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">3</span>
                    <span class="stat-label">搜索模式</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">6</span>
                    <span class="stat-label">筛选维度</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">350px</span>
                    <span class="stat-label">滚动容器</span>
                </div>
            </div>
        </div>

        <div class="bento-grid">
            <!-- 系统概述 - 大卡片 -->
            <div class="bento-card card-wide card-overview">
                <div class="card-header">
                    <div class="card-icon">🚀</div>
                    <div>
                        <div class="card-title">四大核心功能，让查找变成享受</div>
                        <div class="card-subtitle">从"笔记工具"升级为"智能知识大脑"</div>
                    </div>
                </div>

                <div class="action-grid">
                    <div class="action-item">
                        <div class="action-title">🚀 顶部快速搜索</div>
                        <div class="action-desc">一键搜索热门关键词，告别繁琐手动输入</div>
                    </div>
                    <div class="action-item">
                        <div class="action-title">🎯 智能多维筛选</div>
                        <div class="action-desc">按领域、时间、健康类型精准筛选</div>
                    </div>
                    <div class="action-item">
                        <div class="action-title">📊 滚动查看大数据</div>
                        <div class="action-desc">350px滚动容器，上千条笔记丝滑浏览</div>
                    </div>
                    <div class="action-item">
                        <div class="action-title">📈 可视化统计分析</div>
                        <div class="action-desc">综合统计卡片，知识积累一目了然</div>
                    </div>
                </div>

                <div class="highlight-box">
                    <div class="highlight-title">实际使用效果</div>
                    <div class="highlight-desc">以前找一条笔记要翻半天，现在3秒钟精准定位！日记越写越多也不慌，滚动查看超流畅！数据统计让我看到自己的成长轨迹，满满动力！</div>
                </div>

                <div class="tags">
                    <span class="tag">智能查询</span>
                    <span class="tag">3秒定位</span>
                    <span class="tag">大数据支持</span>
                    <span class="tag">成长可视化</span>
                </div>
            </div>

            <!-- 快速搜索功能 -->
            <div class="bento-card card-search">
                <div class="card-header">
                    <div class="card-icon">🔍</div>
                    <div>
                        <div class="card-title">顶部快速搜索</div>
                        <div class="card-subtitle">贴心助手，瞬间定位内容</div>
                    </div>
                </div>

                <ul class="features-list">
                    <li>预设热门关键词：生活、Obsidian、小红书等</li>
                    <li>一键搜索响应，告别繁琐手动输入</li>
                    <li>智能关键词匹配，支持模糊搜索</li>
                    <li>即时搜索响应，点击按钮即可定位</li>
                    <li>支持多关键词组合搜索</li>
                </ul>

                <div style="margin-top: 15px; padding: 15px; background: rgba(59, 130, 246, 0.1); border-radius: 8px;">
                    <div style="font-size: 0.9rem; color: #3b82f6; font-weight: 600;">热门关键词</div>
                    <div style="font-size: 0.8rem; color: #a0a0a0; margin-top: 5px;">
                        生活、知识管理、输出、模板、Augment、Prompt、Obsidian、小红书
                    </div>
                </div>

                <div class="tags">
                    <span class="tag">一键搜索</span>
                    <span class="tag">热门关键词</span>
                    <span class="tag">即时响应</span>
                </div>
            </div>

            <!-- 智能筛选功能 -->
            <div class="bento-card card-filter">
                <div class="card-header">
                    <div class="card-icon">🎯</div>
                    <div>
                        <div class="card-title">智能多维筛选</div>
                        <div class="card-subtitle">精准筛选，一个按钮搞定</div>
                    </div>
                </div>

                <div class="action-grid">
                    <div class="action-item">
                        <div class="action-title">领域筛选</div>
                        <div class="action-desc">系统、健康、精神、财富、传承、生活</div>
                    </div>
                    <div class="action-item">
                        <div class="action-title">时间筛选</div>
                        <div class="action-desc">最近7天、30天、90天、180天</div>
                    </div>
                    <div class="action-item">
                        <div class="action-title">健康细分</div>
                        <div class="action-desc">精力、调理、中医、西医、习惯、恢复</div>
                    </div>
                    <div class="action-item">
                        <div class="action-title">搜索模式</div>
                        <div class="action-desc">AND(所有)、OR(任一)、EXACT(精确)</div>
                    </div>
                </div>

                <div class="tags">
                    <span class="tag">多维筛选</span>
                    <span class="tag">精准定位</span>
                    <span class="tag">逻辑运算</span>
                </div>
            </div>

            <!-- 滚动查看体验 -->
            <div class="bento-card card-scroll">
                <div class="card-header">
                    <div class="card-icon">📱</div>
                    <div>
                        <div class="card-title">滚动查看大数据</div>
                        <div class="card-subtitle">再也不怕记录太多</div>
                    </div>
                </div>

                <ul class="features-list">
                    <li>350px滚动容器，优化查看高度</li>
                    <li>智能数量提示，超过5条显示总数</li>
                    <li>上千条笔记也能丝滑浏览</li>
                    <li>响应式设计，适配不同屏幕</li>
                    <li>日期跳转，点击直达原始位置</li>
                </ul>

                <div style="margin-top: 15px; padding: 15px; background: rgba(139, 92, 246, 0.1); border-radius: 8px;">
                    <div style="font-size: 0.9rem; color: #8b5cf6; font-weight: 600;">体验升级</div>
                    <div style="font-size: 0.8rem; color: #a0a0a0; margin-top: 5px;">
                        体验感直接拉满！关键词高亮显示，便于快速定位
                    </div>
                </div>

                <div class="tags">
                    <span class="tag">滚动查看</span>
                    <span class="tag">大数据支持</span>
                    <span class="tag">丝滑体验</span>
                </div>
            </div>

            <!-- 可视化统计 -->
            <div class="bento-card card-stats">
                <div class="card-header">
                    <div class="card-icon">📈</div>
                    <div>
                        <div class="card-title">可视化统计分析</div>
                        <div class="card-subtitle">让你的知识积累一目了然，成就感爆棚</div>
                    </div>
                </div>

                <ul class="features-list">
                    <li>综合统计卡片：总体统计、月度趋势、活跃时间段</li>
                    <li>领域分布图表：各领域记录数量和占比可视化</li>
                    <li>紧凑布局设计：信息密度高且易读</li>
                    <li>成长轨迹追踪：数据统计展现知识积累</li>
                    <li>多维度分析：时间趋势、活跃度、记录频率</li>
                </ul>

                <div style="margin-top: 15px; padding: 15px; background: rgba(249, 115, 22, 0.1); border-radius: 8px; border-left: 3px solid #f97316;">
                    <strong>统计维度:</strong> 领域分布、时间趋势、活跃度分析、记录频率、内容类型分析等多维度数据展示
                </div>

                <div class="tags">
                    <span class="tag">可视化统计</span>
                    <span class="tag">成长追踪</span>
                    <span class="tag">多维分析</span>
                    <span class="tag">进度条展示</span>
                </div>
            </div>

        </div>
    </div>
</body>
</html>
