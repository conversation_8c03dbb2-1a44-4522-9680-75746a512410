# Python脚本打包演示项目

## 📋 项目说明
这是一个完整的Python脚本打包演示项目，展示了如何将Python脚本打包成独立的可执行文件。

## 📁 文件结构
```
packaging_demo/
├── demo_script.py          # 演示脚本（系统信息查看器）
├── requirements.txt        # 依赖包列表
├── build_script.bat       # 自动打包批处理脚本
├── advanced_build.spec    # 高级打包配置文件
└── README.md              # 项目说明文档
```

## 🚀 快速开始

### 方法1：使用批处理脚本（推荐）
1. 双击运行 `build_script.bat`
2. 等待自动完成环境检查、依赖安装和打包
3. 在 `dist` 文件夹中找到生成的可执行文件

### 方法2：手动打包
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 基础打包
pyinstaller --onefile demo_script.py

# 3. 高级打包（使用配置文件）
pyinstaller advanced_build.spec
```

### 方法3：使用GUI工具
```bash
# 安装并启动auto-py-to-exe
pip install auto-py-to-exe
auto-py-to-exe
```

## 🔧 演示脚本功能
`demo_script.py` 是一个系统信息查看器，具有以下功能：
- 显示详细的系统信息（CPU、内存、磁盘、网络）
- 保存信息到JSON文件
- 实时刷新系统状态
- 友好的交互界面

## 📊 打包结果对比

| 打包方式 | 文件大小 | 启动速度 | 兼容性 |
|----------|----------|----------|--------|
| 基础打包 | ~15MB | 中等 | 良好 |
| 优化打包 | ~12MB | 较快 | 良好 |
| UPX压缩 | ~8MB | 较慢 | 一般 |

## 🎯 学习要点

### 1. 基础打包命令
```bash
# 最简单的打包
pyinstaller script.py

# 打包为单文件
pyinstaller --onefile script.py

# 无控制台窗口
pyinstaller --onefile --windowed script.py
```

### 2. 常用参数说明
- `--onefile`: 打包为单个可执行文件
- `--windowed`: 不显示控制台窗口（GUI应用）
- `--icon=icon.ico`: 设置自定义图标
- `--name=AppName`: 设置输出文件名
- `--add-data`: 添加数据文件
- `--hidden-import`: 添加隐藏导入
- `--exclude-module`: 排除不需要的模块

### 3. 优化技巧
- 使用虚拟环境减少依赖
- 排除不需要的模块
- 使用UPX压缩（可选）
- 合理配置spec文件

### 4. 常见问题解决
- **模块找不到**: 使用 `--hidden-import`
- **文件路径错误**: 使用 `sys._MEIPASS` 处理资源路径
- **启动慢**: 减少导入的模块，使用文件夹模式
- **文件太大**: 排除不需要的模块，使用虚拟环境

## 🔍 进阶学习

### 1. 自定义spec文件
spec文件提供了更精细的控制：
- 精确控制包含/排除的模块
- 添加版本信息和图标
- 配置UPX压缩
- 设置运行时选项

### 2. 跨平台打包
- Windows: 生成 .exe 文件
- macOS: 生成 .app 包
- Linux: 生成可执行文件

### 3. 高级功能
- 代码混淆保护
- 数字签名
- 自动更新机制
- 安装包制作

## 📚 参考资源
- [PyInstaller官方文档](https://pyinstaller.readthedocs.io/)
- [auto-py-to-exe项目](https://github.com/brentvollebregt/auto-py-to-exe)
- [Python打包指南](https://packaging.python.org/)

## ⚠️ 注意事项
1. 打包后的文件较大，包含完整的Python运行环境
2. 首次启动可能较慢，因为需要解压临时文件
3. 某些杀毒软件可能误报，建议添加白名单
4. 跨平台分发需要在目标平台上重新打包

---
*本项目仅用于学习和演示目的*
