# Augment Agent 记忆手动备份快速命令

> 📅 创建时间：2025-08-14 星期四
> 🎯 用途：提供简单易用的手动备份命令
> ⚡ 特点：无需管理员权限，即时执行

## 🚀 一键备份命令

### 标准手动备份
```powershell
cd "C:\Users\<USER>\Desktop\测试库"
.\scripts\auto-backup-memory.ps1 -BackupType manual -Force
```

### 简化版本（在项目目录下）
```powershell
.\scripts\auto-backup-memory.ps1 -BackupType manual -Force
```

### 超简化版本（创建快捷方式）
```powershell
# 创建桌面快捷方式的PowerShell命令
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("$Home\Desktop\备份记忆.lnk")
$Shortcut.TargetPath = "powershell.exe"
$Shortcut.Arguments = "-ExecutionPolicy Bypass -Command `"cd 'C:\Users\<USER>\Desktop\测试库'; .\scripts\auto-backup-memory.ps1 -BackupType manual -Force; pause`""
$Shortcut.WorkingDirectory = "C:\Users\<USER>\Desktop\测试库"
$Shortcut.IconLocation = "shell32.dll,16"
$Shortcut.Description = "Augment Agent 记忆手动备份"
$Shortcut.Save()
Write-Host "✅ 桌面快捷方式已创建：备份记忆.lnk"
```

## 📋 备份内容说明

每次执行手动备份会创建：

### 1. 全局记忆备份模板
- **文件名**：`Augment-Agent全局记忆备份-YYYYMMDD.md`
- **内容**：备份模板和检查清单
- **用途**：提醒手动更新实际记忆内容

### 2. 项目记忆备份清单
- **文件名**：`寸止MCP项目记忆备份-YYYYMMDD.md`
- **内容**：项目记忆文件统计和清单
- **用途**：记录项目特定记忆状态

### 3. Git 版本控制
- **自动提交**：备份文件自动加入版本控制
- **提交信息**：包含时间戳的标准化提交信息
- **历史追踪**：可通过 Git 历史查看备份记录

## ⏰ 建议备份频率

### 日常使用
- **重要变更后**：修改核心工作偏好时立即备份
- **项目节点**：完成重要项目阶段时备份
- **定期维护**：每周或每月定期备份

### 特殊情况
- **系统更新前**：Windows 或 Augment 更新前备份
- **配置变更前**：修改 MCP 配置前备份
- **长期不用前**：暂停使用 Augment 前备份

## 🔍 备份验证命令

### 检查最新备份
```powershell
# 查看最新备份文件
Get-ChildItem "C:\Users\<USER>\Desktop\测试库\backup" -Filter "*备份*.md" | Sort-Object LastWriteTime -Descending | Select-Object -First 3

# 查看备份日志
Get-Content "C:\Users\<USER>\Desktop\测试库\backup\backup-log.txt" -Tail 10

# 检查最后备份记录
Get-Content "C:\Users\<USER>\Desktop\测试库\backup\last-backup.txt" | ConvertFrom-Json
```

### 验证 Git 提交
```powershell
# 查看备份相关的 Git 提交
git log --oneline --grep="备份" -5

# 查看最新提交
git log --oneline -3
```

## 🛠️ 故障排除

### 常见问题

#### 1. 执行策略错误
```powershell
# 问题：无法执行脚本
# 解决：设置执行策略
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### 2. Git 配置问题
```powershell
# 问题：Git 提交失败
# 解决：配置用户信息
git config --global user.email "<EMAIL>"
git config --global user.name "TestLib Admin"
```

#### 3. 路径问题
```powershell
# 问题：找不到脚本文件
# 解决：确认当前目录
Get-Location
cd "C:\Users\<USER>\Desktop\测试库"
```

#### 4. 权限问题
```powershell
# 问题：无法创建文件
# 解决：检查目录权限
Test-Path "C:\Users\<USER>\Desktop\测试库\backup" -PathType Container
```

## 📊 备份状态检查

### 快速状态检查
```powershell
# 一键检查备份状态
Write-Host "=== 备份状态检查 ===" -ForegroundColor Green
Write-Host "项目路径: $(Get-Location)" -ForegroundColor Cyan
Write-Host "最新备份:" -ForegroundColor Yellow
Get-ChildItem "backup" -Filter "*备份*.md" | Sort-Object LastWriteTime -Descending | Select-Object -First 1 | Format-Table Name, LastWriteTime
Write-Host "Git 状态:" -ForegroundColor Yellow
git status --porcelain backup/
Write-Host "最近提交:" -ForegroundColor Yellow
git log --oneline -1
```

## 🎯 最佳实践

### 备份前检查
1. **确认项目路径**：确保在正确的项目目录下
2. **检查 Git 状态**：确保没有未提交的重要变更
3. **验证脚本存在**：确认备份脚本文件完整

### 备份后验证
1. **检查文件生成**：确认备份文件已创建
2. **查看日志内容**：检查备份过程是否有错误
3. **验证 Git 提交**：确认文件已加入版本控制

### 定期维护
1. **清理旧备份**：删除过期的备份文件（保留最近3个月）
2. **更新备份内容**：手动更新全局记忆备份的实际内容
3. **测试恢复流程**：定期测试备份文件的恢复可用性

---

## 💡 小贴士

- **快捷键**：可以为备份命令创建 PowerShell 别名
- **批处理**：可以创建 .bat 文件简化命令执行
- **定时提醒**：可以设置日历提醒定期执行备份
- **云同步**：backup 文件夹可以同步到云存储服务

**记住**：手动备份虽然简单，但需要养成定期执行的习惯！
