# Remember 功能故障应急方案

> 📅 创建时间：2025-08-14 星期四
> 🎯 目的：当 Remember 功能也无法使用时的备用记忆管理方案
> ⚡ 特点：多重备用方案，确保记忆数据不丢失

## 🚨 故障场景分析

### Remember 功能可能的问题
- **服务不可用**：Augment 服务器问题导致 remember 命令失效
- **账号权限问题**：新账号或重置账号没有记忆功能权限
- **数据同步问题**：记忆数据无法正确保存或读取
- **版本兼容问题**：Augment 更新后 remember 功能变更
- **网络连接问题**：无法连接到记忆存储服务

## 🛡️ 三重备用方案

### 方案一：项目配置文件备份（推荐）

#### 1. 更新 .augment-guidelines 文件
```markdown
# 在项目根目录的 .augment-guidelines 文件中添加：

## 🧠 核心工作偏好（Remember 备份）

### 基础交流偏好
- 使用中文交流
- 生成总结性Markdown文档
- 不生成测试脚本
- 需要时协助编译和运行

### 翻译规则模板
当用户说"翻译 XXX文件"时：
1. 仅翻译英文文本，保持代码片段、技术术语、文件路径、命令行指令、YAML元数据不变
2. 确保翻译准确、自然、流畅，符合中文表达习惯
3. 保持完整的Markdown格式结构
4. 保存为 `原文件名_zh.md` 格式到当前工作目录
5. 工作流程：读取原文件 → 翻译 → 保存 → 确认完成

### 对话导出约定
- "导出对话" = 完整原始对话记录
- "导出对话：[主题]" = 结构化整理版
- "保存重要对话" = 深度分析版

### MCP工具优先使用顺序
1. 寸止MCP - 用户反馈交互和项目记忆管理
2. Context7 - 查询最新库文档示例
3. Playwright - 浏览器自动化操作
4. sequential-thinking - 复杂任务分解与思考
5. shrimp-task-manager - 任务规划和管理

### 知识重构原则
1. 模式识别 > 细节记忆
2. 生动形象 > 抽象概念
3. 情感共鸣 > 理性说服
4. 连接已知 > 全新信息
5. 可行洞察 > 纯粹知识
```

#### 2. 创建专用配置文件
```powershell
# 创建独立的工作偏好配置文件
New-Item -Path ".\config\augment-preferences.md" -ItemType File -Force
```

### 方案二：寸止MCP项目记忆（次选）

#### 1. 使用寸止MCP存储核心偏好
```markdown
# 通过寸止MCP的记忆功能存储：
寸止记忆添加：用户偏好中文交流，生成总结性Markdown文档，不生成测试脚本，需要时协助编译和运行
寸止记忆添加：翻译规则模板完整定义
寸止记忆添加：对话导出约定三种模式
寸止记忆添加：MCP工具优先使用顺序
寸止记忆添加：知识重构五大原则
```

#### 2. 验证寸止MCP记忆功能
```markdown
# 测试寸止MCP是否可用
寸止记忆回忆：查看项目记忆内容
```

### 方案三：本地文档备份（兜底）

#### 1. 创建本地偏好文档
```markdown
# 文件：./backup/本地工作偏好.md
# 内容：完整的工作偏好和规则说明
# 用途：手动参考和新对话时的快速配置
```

#### 2. 对话开始时手动声明
```markdown
# 每次新对话开始时，手动告知 AI：
"请参考项目根目录的 .augment-guidelines 文件中的工作偏好设置"
"我的核心偏好：中文交流、生成总结性Markdown、不生成测试脚本、协助编译运行"
```

## 🔧 应急操作流程

### 第一步：诊断 Remember 功能
```markdown
# 测试 Remember 是否可用
remember: 测试记忆功能是否正常

# 如果失败，检查错误信息：
- 网络连接问题
- 服务不可用
- 权限问题
- 功能变更
```

### 第二步：启用备用方案
```powershell
# 方案一：更新项目配置文件
# 1. 编辑 .augment-guidelines
# 2. 添加核心偏好内容
# 3. 每次对话开始时提醒 AI 查看

# 方案二：使用寸止MCP
# 1. 测试寸止MCP记忆功能
# 2. 逐条添加核心偏好
# 3. 验证记忆存储成功

# 方案三：本地文档备份
# 1. 创建本地偏好文档
# 2. 每次对话手动声明偏好
# 3. 保持文档及时更新
```

### 第三步：验证备用方案效果
```markdown
# 测试核心功能：
1. 中文交流是否正常
2. 文档生成偏好是否生效
3. 翻译规则是否被遵循
4. MCP工具调用是否正确
5. 对话导出是否按约定执行
```

## 📋 应急配置模板

### 快速配置命令
```powershell
# 创建应急配置文件
@"
# Augment Agent 应急工作偏好

## 核心偏好
- 中文交流
- 生成总结性Markdown文档
- 不生成测试脚本
- 协助编译和运行

## 翻译规则
翻译 XXX文件 = 仅翻译英文，保持代码不变，保存为原文件名_zh.md

## 对话导出
- 导出对话 = 完整记录
- 导出对话：主题 = 结构化版
- 保存重要对话 = 深度分析版

## MCP工具顺序
寸止MCP > Context7 > Playwright > sequential-thinking > shrimp-task-manager
"@ | Out-File -FilePath ".\config\emergency-preferences.md" -Encoding UTF8
```

### 对话开始时的标准声明
```markdown
"请注意我的工作偏好：
1. 使用中文交流
2. 生成总结性Markdown文档，不生成测试脚本
3. 需要时协助编译和运行
4. 优先使用寸止MCP进行用户交互
5. 遵循翻译规则模板和对话导出约定
详细偏好请参考项目的 .augment-guidelines 文件"
```

## ⚠️ 注意事项

### 备用方案的限制
- **手动操作**：需要每次对话开始时手动提醒
- **一致性**：可能出现不同对话中偏好不一致的情况
- **维护成本**：需要同时维护多个配置文件
- **功能限制**：无法实现 Remember 的跨对话记忆功能

### 最佳实践
- **定期检查**：每周检查 Remember 功能是否恢复
- **同步更新**：偏好变更时同时更新所有备用方案
- **优先级**：Remember 恢复后立即迁移回主要方案
- **文档维护**：保持应急方案文档的及时更新

## 🔄 恢复策略

### Remember 功能恢复后
1. **验证功能**：测试 remember 命令是否正常
2. **迁移数据**：从备用方案迁移回 Remember
3. **清理备用**：清理临时的应急配置
4. **更新备份**：更新备份文档中的恢复指南

### 长期无法恢复时
1. **接受现状**：将备用方案作为主要方案
2. **优化流程**：改进手动配置的效率
3. **自动化**：开发脚本自动应用偏好设置
4. **替代方案**：考虑其他记忆管理工具

---

## 💡 总结

Remember 功能故障时，我们有三重保护：
1. **项目配置文件**：.augment-guidelines 作为主要备用
2. **寸止MCP记忆**：项目特定的记忆管理
3. **本地文档备份**：最后的兜底方案

**关键原则**：多重备份，分层保护，确保在任何情况下都能维持基本的工作偏好！
