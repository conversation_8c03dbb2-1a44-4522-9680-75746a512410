<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment Agent工作偏好设置</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: #1a1d29;
            color: #ffffff;
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 60px 40px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 80px;
        }
        
        .main-title {
            font-size: 3.5rem;
            font-weight: 700;
            color: #00ff88;
            margin-bottom: 15px;
            letter-spacing: -1px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #8892b0;
            margin-bottom: 20px;
            font-weight: 400;
        }
        
        .description {
            font-size: 1rem;
            color: #64748b;
            margin-bottom: 50px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .stats-section {
            display: flex;
            justify-content: center;
            gap: 80px;
            margin-bottom: 80px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #00ff88;
            margin-bottom: 8px;
            display: block;
        }
        
        .stat-label {
            font-size: 1rem;
            color: #8892b0;
            font-weight: 500;
        }
        
        .bento-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            grid-auto-rows: minmax(250px, auto);
        }
        
        .bento-card {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 20px;
            padding: 35px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .bento-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color);
            border-radius: 20px 20px 0 0;
        }
        
        .bento-card:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.05);
            border-color: var(--card-color);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .card-large {
            grid-column: span 2;
            grid-row: span 2;
        }
        
        .card-wide {
            grid-column: span 2;
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .card-icon {
            font-size: 2.5rem;
            margin-right: 15px;
            color: var(--card-color);
        }
        
        .card-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #ffffff;
        }
        
        .card-subtitle {
            font-size: 0.9rem;
            color: #8892b0;
            margin-top: 5px;
        }
        
        .workflow {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 25px 0;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .workflow-step {
            text-align: center;
            flex: 1;
        }
        
        .step-icon {
            width: 50px;
            height: 50px;
            background: var(--card-color);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 1.5rem;
            color: #1a1d29;
            font-weight: 600;
        }
        
        .step-label {
            font-size: 0.8rem;
            color: #8892b0;
            font-weight: 500;
        }
        
        .workflow-arrow {
            font-size: 1.2rem;
            color: var(--card-color);
            margin: 0 10px;
        }
        
        .features-list {
            list-style: none;
            margin-bottom: 20px;
        }
        
        .features-list li {
            padding: 8px 0;
            color: #cbd5e1;
            font-size: 0.9rem;
            position: relative;
            padding-left: 20px;
        }
        
        .features-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--card-color);
            font-weight: 600;
        }
        
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .tag {
            background: rgba(255, 255, 255, 0.1);
            color: var(--card-color);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .preference-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        
        .preference-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .preference-title {
            font-weight: 600;
            color: var(--card-color);
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .preference-desc {
            font-size: 0.8rem;
            color: #a0a0a0;
        }
        
        /* 卡片颜色主题 */
        .card-workflow { --card-color: #00ff88; }
        .card-mcp { --card-color: #3b82f6; }
        .card-knowledge { --card-color: #06b6d4; }
        .card-obsidian { --card-color: #8b5cf6; }
        .card-tech { --card-color: #f97316; }
        .card-communication { --card-color: #ec4899; }
        .card-automation { --card-color: #eab308; }
        .card-business { --card-color: #ef4444; }
        
        @media (max-width: 1024px) {
            .bento-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
            }
            
            .card-large,
            .card-wide {
                grid-column: span 1;
            }
            
            .stats-section {
                gap: 50px;
            }
        }
        
        @media (max-width: 768px) {
            .bento-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .stats-section {
                gap: 30px;
            }
            
            .main-title {
                font-size: 2.5rem;
            }
            
            .container {
                padding: 40px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">Augment Agent 工作偏好设置</h1>
            <p class="subtitle">基于 Claude Sonnet 4 的智能编程助手配置指南</p>
            <p class="description">系统化的工作流程 • 智能化的任务管理 • 个性化的协作偏好 • 高效的开发体验</p>
            
            <div class="stats-section">
                <div class="stat-item">
                    <span class="stat-number">3</span>
                    <span class="stat-label">核心阶段</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">5</span>
                    <span class="stat-label">MCP服务</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">8</span>
                    <span class="stat-label">偏好模块</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">v1.0</span>
                    <span class="stat-label">当前版本</span>
                </div>
            </div>
        </div>
        
        <div class="bento-grid">
            <!-- 核心工作流程 - 大卡片 -->
            <div class="bento-card card-large card-workflow">
                <div class="card-header">
                    <div class="card-icon">🎯</div>
                    <div>
                        <div class="card-title">核心工作流程</div>
                        <div class="card-subtitle">三阶段任务执行体系</div>
                    </div>
                </div>
                
                <div class="workflow">
                    <div class="workflow-step">
                        <div class="step-icon">📋</div>
                        <div class="step-label">任务分析</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">⚡</div>
                        <div class="step-label">执行反馈</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">📊</div>
                        <div class="step-label">复盘总结</div>
                    </div>
                </div>
                
                <div class="preference-grid">
                    <div class="preference-item">
                        <div class="preference-title">阶段1：计划制定</div>
                        <div class="preference-desc">复杂任务自动创建计划文档，命名格式：核心功能-YYYYMMDD</div>
                    </div>
                    <div class="preference-item">
                        <div class="preference-title">阶段2：执行反馈</div>
                        <div class="preference-desc">严格按计划执行，关键节点使用interactive-feedback反馈</div>
                    </div>
                    <div class="preference-item">
                        <div class="preference-title">阶段3：复盘总结</div>
                        <div class="preference-desc">创建复盘文档，包含问题分析、解决方案、经验总结</div>
                    </div>
                    <div class="preference-item">
                        <div class="preference-title">深度分析机制</div>
                        <div class="preference-desc">主动使用sequential-thinking，透明化思考过程</div>
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">任务规划</span>
                    <span class="tag">执行跟踪</span>
                    <span class="tag">经验积累</span>
                    <span class="tag">持续优化</span>
                </div>
            </div>
            
            <!-- MCP服务优先使用 -->
            <div class="bento-card card-mcp">
                <div class="card-header">
                    <div class="card-icon">🛠️</div>
                    <div>
                        <div class="card-title">MCP服务优先使用</div>
                        <div class="card-subtitle">核心服务工具箱</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li>interactive_feedback - 用户反馈交互</li>
                    <li>sequential-thinking - 复杂任务分解与深度思考</li>
                    <li>Context7 - 查询最新库文档/示例</li>
                    <li>Playwright - 浏览器自动化操作</li>
                    <li>together-image-gen - 图像生成</li>
                </ul>
                
                <div class="tags">
                    <span class="tag">智能交互</span>
                    <span class="tag">文档查询</span>
                    <span class="tag">自动化操作</span>
                </div>
            </div>
            
            <!-- 知识重构原则 -->
            <div class="bento-card card-knowledge">
                <div class="card-header">
                    <div class="card-icon">🧠</div>
                    <div>
                        <div class="card-title">知识重构原则</div>
                        <div class="card-subtitle">智能化内容转化</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li>模式识别 > 细节记忆</li>
                    <li>生动形象 > 抽象概念</li>
                    <li>情感共鸣 > 理性说服</li>
                    <li>连接已知 > 全新信息</li>
                    <li>可行洞察 > 纯粹知识</li>
                </ul>
                
                <div style="margin-top: 15px; padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px;">
                    <div style="font-size: 0.9rem; color: #06b6d4; font-weight: 600;">处理流程</div>
                    <div style="font-size: 0.8rem; color: #a0a0a0; margin-top: 5px;">
                        用户提取文本 → AI总结组织 → 先个别后整合
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">化繁为简</span>
                    <span class="tag">化虚为实</span>
                    <span class="tag">保持准确</span>
                </div>
            </div>
            
            <!-- Obsidian系统偏好 - 宽卡片 -->
            <div class="bento-card card-wide card-obsidian">
                <div class="card-header">
                    <div class="card-icon">📊</div>
                    <div>
                        <div class="card-title">Obsidian系统偏好</div>
                        <div class="card-subtitle">完整的知识管理生态</div>
                    </div>
                </div>
                
                <div class="preference-grid">
                    <div class="preference-item">
                        <div class="preference-title">知识管理系统</div>
                        <div class="preference-desc">问题导向框架、文档属性标签、可视化仪表盘</div>
                    </div>
                    <div class="preference-item">
                        <div class="preference-title">精力管理系统</div>
                        <div class="preference-desc">健康数据记录、关键词查询、可视化图表</div>
                    </div>
                    <div class="preference-item">
                        <div class="preference-title">任务管理系统</div>
                        <div class="preference-desc">简约三栏布局、进度指示器、项目链接迁移</div>
                    </div>
                    <div class="preference-item">
                        <div class="preference-title">项目管理系统</div>
                        <div class="preference-desc">状态属性管理、彩色图标显示、进度跟踪</div>
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">知识管理</span>
                    <span class="tag">任务跟踪</span>
                    <span class="tag">项目管理</span>
                    <span class="tag">数据可视化</span>
                </div>
            </div>
            
            <!-- 技术实现偏好 -->
            <div class="bento-card card-tech">
                <div class="card-header">
                    <div class="card-icon">🛠️</div>
                    <div>
                        <div class="card-title">技术实现偏好</div>
                        <div class="card-subtitle">标准化开发规范</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li>严格使用包管理器（npm、pip、cargo）</li>
                    <li>禁止手动编辑配置文件</li>
                    <li>自动处理依赖和版本冲突</li>
                    <li>标准化代码显示规范</li>
                    <li>高质量JPG图像生成偏好</li>
                </ul>
                
                <div class="tags">
                    <span class="tag">包管理器</span>
                    <span class="tag">代码规范</span>
                    <span class="tag">自动化</span>
                </div>
            </div>
            
            <!-- 沟通协作偏好 -->
            <div class="bento-card card-communication">
                <div class="card-header">
                    <div class="card-icon">💡</div>
                    <div>
                        <div class="card-title">沟通协作偏好</div>
                        <div class="card-subtitle">高效协作原则</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li>中文交流为主，专业术语英文</li>
                    <li>诚实告知限制，提供替代方案</li>
                    <li>重要决策前协作讨论</li>
                    <li>标准日期格式：YYYY-MM-DD ddd WW</li>
                    <li>视觉丰富的卡片式设计偏好</li>
                </ul>
                
                <div class="tags">
                    <span class="tag">中文优先</span>
                    <span class="tag">诚实透明</span>
                    <span class="tag">协作讨论</span>
                </div>
            </div>
            
            <!-- 自动化与部署 -->
            <div class="bento-card card-automation">
                <div class="card-header">
                    <div class="card-icon">🚀</div>
                    <div>
                        <div class="card-title">自动化与部署</div>
                        <div class="card-subtitle">高效执行策略</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li>直接命令行执行Python脚本</li>
                    <li>创建.bat文件便于重复使用</li>
                    <li>一次性部署，可重复使用</li>
                    <li>结构化Markdown输出</li>
                    <li>支持批量处理</li>
                </ul>
                
                <div class="tags">
                    <span class="tag">脚本自动化</span>
                    <span class="tag">批量处理</span>
                    <span class="tag">可重复使用</span>
                </div>
            </div>
            
            <!-- 商业化考虑 -->
            <div class="bento-card card-business">
                <div class="card-header">
                    <div class="card-icon">📈</div>
                    <div>
                        <div class="card-title">商业化考虑</div>
                        <div class="card-subtitle">模板销售策略</div>
                    </div>
                </div>
                
                <div class="preference-grid">
                    <div class="preference-item">
                        <div class="preference-title">目标平台</div>
                        <div class="preference-desc">小红书</div>
                    </div>
                    <div class="preference-item">
                        <div class="preference-title">价格区间</div>
                        <div class="preference-desc">9.9元 - 79.9元</div>
                    </div>
                    <div class="preference-item">
                        <div class="preference-title">产品类型</div>
                        <div class="preference-desc">Obsidian管理系统模板</div>
                    </div>
                    <div class="preference-item">
                        <div class="preference-title">营销重点</div>
                        <div class="preference-desc">治愈系奶茶风、Morandi配色</div>
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">模板销售</span>
                    <span class="tag">治愈系设计</span>
                    <span class="tag">小红书营销</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
