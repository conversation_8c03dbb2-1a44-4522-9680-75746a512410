import os
import asyncio
from playwright.async_api import async_playwright

async def generate_image_from_html(html_file, output_file):
    """
    使用Playwright从HTML文件生成图片
    
    Args:
        html_file: HTML文件路径
        output_file: 输出PNG文件路径
    """
    abs_html_path = os.path.abspath(html_file)
    file_url = f"file://{abs_html_path}"
    
    print(f"生成图片: {html_file} -> {output_file}")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page(viewport={"width": 1400, "height": 1050})
        
        await page.goto(file_url)
        await page.wait_for_load_state("networkidle")
        
        # 等待一下确保页面完全加载
        await asyncio.sleep(2)
        
        # 找到需要截图的元素
        promo_container = await page.query_selector(".promo-container")
        if promo_container:
            await promo_container.screenshot(path=output_file)
            print(f"图片已成功保存到: {output_file}")
        else:
            print("找不到.promo-container元素")
        
        await browser.close()

async def main():
    # 设置文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    html_file = os.path.join(current_dir, "obsidian_task_hexagon_promo.html")
    output_file = os.path.join(current_dir, "obsidian_task_hexagon_promo.png")
    
    # 生成图片
    await generate_image_from_html(html_file, output_file)

if __name__ == "__main__":
    asyncio.run(main()) 