# 记忆备份与恢复机制

> 📅 创建时间：2025-08-14 星期四
> 🎯 目的：建立完整的记忆数据安全保护体系
> 🔒 重要性：防止账号问题导致的工作偏好和配置丢失

## 🏗️ 三层记忆架构

### 第一层：Augment Remember（全局记忆）
- **存储位置**：Augment 账号云端
- **内容范围**：跨项目的工作偏好、长期协作原则
- **风险等级**：⚠️ 高风险（账号依赖）
- **备份方式**：定期导出到本地文档

### 第二层：寸止MCP（项目记忆）
- **存储位置**：项目本地 git 仓库
- **内容范围**：项目特定规则、临时上下文
- **风险等级**：🟡 中风险（本地文件）
- **备份方式**：git 版本控制

### 第三层：项目配置文件（静态备份）
- **存储位置**：`.augment-guidelines`、`backup/` 文件夹
- **内容范围**：核心工作原则、重要配置
- **风险等级**：🟢 低风险（多重备份）
- **备份方式**：文件系统 + git + 云存储

## 📋 备份策略

### 自动备份（推荐）
```bash
# 每月第一个工作日执行
# 1. 导出全局记忆
# 2. 同步到寸止MCP
# 3. 更新项目配置文件
# 4. git 提交备份
```

### 手动备份（应急）
1. **立即备份**：`导出对话：记忆备份`
2. **同步记忆**：重要偏好添加到寸止MCP
3. **文档化**：更新 `.augment-guidelines`
4. **版本控制**：git commit 保存

## 🔄 恢复流程

### 场景1：账号完全丢失
1. 从 `./backup/Augment-Agent全局记忆备份-*.md` 恢复基础配置
2. 逐条使用 `remember` 功能重建全局记忆
3. 验证寸止MCP项目记忆完整性
4. 测试核心工作流程

### 场景2：项目记忆丢失
1. 从 git 历史恢复寸止MCP配置
2. 从全局记忆同步重要规则
3. 重新初始化项目特定设置
4. 验证工具链完整性

### 场景3：部分记忆损坏
1. 对比备份文档识别缺失内容
2. 选择性恢复重要配置
3. 增量更新记忆系统
4. 验证功能正常性

## 📊 备份内容清单

### 核心工作偏好
- [x] 中文交流偏好
- [x] 文档生成规则（生成总结性Markdown，不生成测试脚本）
- [x] 编译运行协助偏好
- [x] 翻译规则模板
- [x] 对话导出约定

### 技术配置
- [x] MCP工具配置经验
- [x] PowerShell使用偏好
- [x] CLI工具配置方法
- [x] 包管理器使用规则

### 协作原则
- [x] 寸止MCP交互规则
- [x] 任务管理偏好
- [x] 知识重构原则
- [x] 项目管理系统偏好

## ⚡ 快速恢复命令

### 恢复全局记忆
```markdown
remember: 用户偏好中文交流，生成总结性Markdown文档，不生成测试脚本，需要时协助编译和运行
remember: 翻译规则：仅翻译英文保持代码不变，准确自然流畅，保持Markdown格式，保存为原文件名_zh.md
remember: 对话导出约定：导出对话=完整记录，导出对话：主题=结构化版，保存重要对话=深度分析版
remember: MCP工具优先使用寸止、Context7、Playwright、sequential-thinking、shrimp-task-manager
remember: 知识重构原则：模式识别>细节记忆，生动形象>抽象概念，情感共鸣>理性说服，连接已知>全新信息，可行洞察>纯粹知识
```

### 恢复项目记忆
```markdown
寸止记忆添加：数据安全备份机制已建立，包含三层记忆架构和完整恢复流程
寸止记忆添加：项目路径标准化约定：任务计划./issues/，任务复盘./rewind/，推广图./cursor_projects/Ob/
寸止记忆添加：MCP服务优先使用顺序：寸止>Context7>Playwright>sequential-thinking>shrimp-task-manager
```

## 🔍 验证检查清单

### 恢复后验证
- [ ] 中文交流正常
- [ ] 寸止MCP交互正常
- [ ] 文档生成偏好正确
- [ ] 翻译功能可用
- [ ] MCP工具链完整
- [ ] 项目路径约定生效
- [ ] 任务管理功能正常

### 定期检查（每月）
- [ ] 备份文档是否最新
- [ ] 全局记忆是否完整
- [ ] 项目记忆是否同步
- [ ] git 备份是否正常
- [ ] 恢复流程是否可用

---

## 📝 使用说明

1. **预防性备份**：每次重要配置变更后立即备份
2. **定期维护**：每月检查和更新备份内容
3. **测试恢复**：定期测试恢复流程确保可用性
4. **多重保护**：同时使用三层记忆架构降低风险
5. **版本管理**：使用 git 跟踪备份文档的变更历史

此机制确保即使在最坏情况下（账号完全丢失），也能快速恢复到可用状态。
