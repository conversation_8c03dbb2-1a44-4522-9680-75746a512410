<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP现代化推广图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 1200px;
            height: 1800px;
            overflow: hidden;
            position: relative;
        }
        
        .background-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(255,255,255,0.2) 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, rgba(255,255,255,0.2) 2px, transparent 2px);
            background-size: 60px 60px;
        }
        
        .container {
            padding: 60px 50px;
            height: 100%;
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 2;
        }
        
        .header {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .main-title {
            font-size: 64px;
            font-weight: 800;
            background: linear-gradient(45deg, #ffffff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 28px;
            color: rgba(255,255,255,0.9);
            margin-bottom: 15px;
            font-weight: 300;
        }
        
        .tagline {
            font-size: 20px;
            color: rgba(255,255,255,0.7);
            font-weight: 300;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            flex: 1;
            margin-bottom: 50px;
        }
        
        .service-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 30px 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #00d2d3);
            background-size: 200% 100%;
            animation: gradient 3s ease infinite;
        }
        
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .service-card:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .service-icon {
            font-size: 40px;
            margin-bottom: 15px;
            display: block;
        }
        
        .service-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #ffffff;
        }
        
        .service-name {
            font-size: 14px;
            color: rgba(255,255,255,0.7);
            margin-bottom: 12px;
            font-family: 'Courier New', monospace;
            background: rgba(0,0,0,0.2);
            padding: 4px 8px;
            border-radius: 6px;
        }
        
        .service-desc {
            font-size: 13px;
            line-height: 1.4;
            color: rgba(255,255,255,0.8);
            flex: 1;
        }
        
        .stats-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            text-align: center;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-size: 14px;
            color: rgba(255,255,255,0.7);
            font-weight: 500;
        }
        
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .floating-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.05);
            animation: float 6s ease-in-out infinite;
        }
        
        .circle1 {
            width: 80px;
            height: 80px;
            top: 20%;
            right: 10%;
            animation-delay: 0s;
        }
        
        .circle2 {
            width: 60px;
            height: 60px;
            bottom: 30%;
            left: 8%;
            animation-delay: 2s;
        }
        
        .circle3 {
            width: 100px;
            height: 100px;
            top: 60%;
            right: 15%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body>
    <div class="background-pattern"></div>
    <div class="floating-elements">
        <div class="floating-circle circle1"></div>
        <div class="floating-circle circle2"></div>
        <div class="floating-circle circle3"></div>
    </div>
    
    <div class="container">
        <div class="header">
            <h1 class="main-title">MCP 服务生态系统</h1>
            <p class="subtitle">9个核心服务 · 完整配置方案 · 开箱即用</p>
            <p class="tagline">Model Context Protocol 标准化部署指南</p>
        </div>
        
        <div class="services-grid">
            <div class="service-card">
                <span class="service-icon">💬</span>
                <h3 class="service-title">交互反馈</h3>
                <div class="service-name">mcp-feedback-enhanced</div>
                <p class="service-desc">用户交互反馈工具，系统信息获取，环境诊断和性能监控</p>
            </div>
            
            <div class="service-card">
                <span class="service-icon">📚</span>
                <h3 class="service-title">知识库集成</h3>
                <div class="service-name">mcp-obsidian</div>
                <p class="service-desc">Obsidian知识库操作，笔记读取、搜索、创建和管理</p>
            </div>
            
            <div class="service-card">
                <span class="service-icon">📖</span>
                <h3 class="service-title">文档查询</h3>
                <div class="service-name">context7</div>
                <p class="service-desc">最新库文档查询，API参考，代码示例和技术资料</p>
            </div>
            
            <div class="service-card">
                <span class="service-icon">🧠</span>
                <h3 class="service-title">智能思维</h3>
                <div class="service-name">sequential-thinking</div>
                <p class="service-desc">结构化思维分析，复杂问题分解和逐步推理</p>
            </div>
            
            <div class="service-card">
                <span class="service-icon">🌐</span>
                <h3 class="service-title">浏览器自动化</h3>
                <div class="service-name">playwright</div>
                <p class="service-desc">网页自动化操作，截图、表单填写和数据抓取</p>
            </div>
            
            <div class="service-card">
                <span class="service-icon">🎨</span>
                <h3 class="service-title">AI图像生成</h3>
                <div class="service-name">replicate-flux</div>
                <p class="service-desc">高质量AI图像生成，多种风格和格式支持</p>
            </div>
            
            <div class="service-card">
                <span class="service-icon">🖼️</span>
                <h3 class="service-title">图像创作</h3>
                <div class="service-name">together-image-gen</div>
                <p class="service-desc">Together AI图像生成，快速创作和批量处理</p>
            </div>
            
            <div class="service-card">
                <span class="service-icon">📋</span>
                <h3 class="service-title">任务管理</h3>
                <div class="service-name">shrimp-task-manager</div>
                <p class="service-desc">智能任务规划执行，项目管理和工作流程优化</p>
            </div>
            
            <div class="service-card">
                <span class="service-icon">🌍</span>
                <h3 class="service-title">网络获取</h3>
                <div class="service-name">fetch</div>
                <p class="service-desc">网页内容获取，API调用，数据抓取和处理</p>
            </div>
        </div>
        
        <div class="stats-section">
            <div class="stat-item">
                <div class="stat-number">9</div>
                <div class="stat-label">核心服务</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">2</div>
                <div class="stat-label">支持IDE</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">98%</div>
                <div class="stat-label">配置成功率</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">24/7</div>
                <div class="stat-label">技术支持</div>
            </div>
        </div>
    </div>
</body>
</html>
