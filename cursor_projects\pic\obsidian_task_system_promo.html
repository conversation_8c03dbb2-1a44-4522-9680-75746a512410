<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obsidian任务管理系统推广图片</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1a1d29 0%, #2a2d3a 100%);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .promo-container {
            width: 1200px;
            height: 1000px;
            position: relative;
            overflow: hidden;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #1a1d29 0%, #2a2d3a 100%);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
        }
        
        .main-title {
            font-size: 48px;
            font-weight: bold;
            color: #00d4aa;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 212, 170, 0.3);
        }
        
        .subtitle {
            font-size: 22px;
            color: #ffffff;
            margin-bottom: 20px;
        }
        
        .description {
            font-size: 18px;
            color: #a0a3b1;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            padding: 0 40px;
        }
        
        .feature-card {
            padding: 30px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            border: 1px solid rgba(0, 212, 170, 0.2);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 230px;
            display: flex;
            flex-direction: column;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            border-color: rgba(0, 212, 170, 0.6);
        }
        
        .feature-icon {
            font-size: 36px;
            color: #00d4aa;
            margin-bottom: 15px;
            text-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
        }
        
        .feature-title {
            font-size: 24px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            font-size: 16px;
            color: #a0a3b1;
            line-height: 1.6;
            flex-grow: 1;
        }
        
        .workflow {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 40px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 20px;
            padding: 20px;
        }
        
        .workflow-step {
            flex: 1;
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .workflow-step:hover {
            background: rgba(0, 212, 170, 0.1);
            transform: scale(1.05);
        }
        
        .step-icon {
            font-size: 24px;
            color: #00d4aa;
            margin-bottom: 10px;
        }
        
        .step-title {
            font-size: 20px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }
        
        .step-desc {
            font-size: 14px;
            color: #a0a3b1;
        }
        
        .workflow-arrow {
            color: #00d4aa;
            font-size: 24px;
            margin: 0 -5px;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            font-size: 14px;
            color: #8b8e9b;
            position: relative;
            z-index: 1;
        }
        
        /* 装饰元素 */
        .decoration {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(0, 212, 170, 0.2), rgba(0, 212, 170, 0.05));
            filter: blur(30px);
            z-index: -1;
        }
        
        .dec1 {
            width: 400px;
            height: 400px;
            top: -100px;
            left: -100px;
        }
        
        .dec2 {
            width: 300px;
            height: 300px;
            bottom: -50px;
            right: -50px;
        }
    </style>
</head>
<body>
    <div class="promo-container">
        <!-- 装饰元素 -->
        <div class="decoration dec1"></div>
        <div class="decoration dec2"></div>
        
        <!-- 标题区域 -->
        <div class="header">
            <div class="main-title">Obsidian 任务管理系统</div>
            <div class="subtitle">高效规划 · 科学执行 · 数据驱动</div>
            <div class="description">基于 Obsidian + Dataview 构建的全方位任务管理解决方案</div>
        </div>
        
        <!-- 功能模块区域 -->
        <div class="features-grid">
            <!-- 功能卡片1: 任务统计卡片 -->
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <div class="feature-title">任务统计卡片</div>
                <div class="feature-desc">
                    七彩卡片直观展示任务分布状态，包括今天、明天、本周、未来及逾期任务，让您对所有任务一目了然，迅速找到工作重点。
                </div>
            </div>
            
            <!-- 功能卡片2: 番茄钟管理 -->
            <div class="feature-card">
                <div class="feature-icon">🍅</div>
                <div class="feature-title">番茄钟管理</div>
                <div class="feature-desc">
                    设定每日番茄钟目标，追踪实际完成情况，系统自动计算完成率并生成统计分析，帮助您持续优化时间管理效率。
                </div>
            </div>
            
            <!-- 功能卡片3: 项目任务分布 -->
            <div class="feature-card">
                <div class="feature-icon">📁</div>
                <div class="feature-title">项目任务分布</div>
                <div class="feature-desc">
                    智能识别项目文件和待整理任务，自动统计已规划、待整理和已完成任务数量，计算项目进度，提供全面项目视图。
                </div>
            </div>
            
            <!-- 功能卡片4: 主子任务关系 -->
            <div class="feature-card">
                <div class="feature-icon">🎯</div>
                <div class="feature-title">主子任务关系</div>
                <div class="feature-desc">
                    强大的任务层级管理功能，通过 @任务名 语法轻松关联主子任务，构建清晰的任务结构树，便于复杂任务的拆分与管理。
                </div>
            </div>
            
            <!-- 功能卡片5: 智能筛选 -->
            <div class="feature-card">
                <div class="feature-icon">🔍</div>
                <div class="feature-title">智能筛选</div>
                <div class="feature-desc">
                    强大的多维度任务过滤系统，支持按优先级、标签、项目和时间范围等条件组合筛选，快速定位需要关注的任务。
                </div>
            </div>
            
            <!-- 功能卡片6: 数据可视化 -->
            <div class="feature-card">
                <div class="feature-icon">📈</div>
                <div class="feature-title">数据可视化</div>
                <div class="feature-desc">
                    自动生成完成率与时间分布图表，直观展示工作效率数据，帮助您发现时间管理模式，持续优化工作习惯。
                </div>
            </div>
        </div>
        
        <!-- 工作流程区域 -->
        <div class="workflow">
            <div class="workflow-step">
                <div class="step-icon">📝</div>
                <div class="step-title">规划</div>
                <div class="step-desc">设定目标与任务分解</div>
            </div>
            
            <div class="workflow-arrow">→</div>
            
            <div class="workflow-step">
                <div class="step-icon">🚀</div>
                <div class="step-title">执行</div>
                <div class="step-desc">专注完成与状态更新</div>
            </div>
            
            <div class="workflow-arrow">→</div>
            
            <div class="workflow-step">
                <div class="step-icon">📊</div>
                <div class="step-title">追踪</div>
                <div class="step-desc">进度监控与数据收集</div>
            </div>
            
            <div class="workflow-arrow">→</div>
            
            <div class="workflow-step">
                <div class="step-icon">🔄</div>
                <div class="step-title">复盘</div>
                <div class="step-desc">效率分析与持续优化</div>
            </div>
        </div>
        
        <!-- 页脚信息 -->
        <div class="footer">
            基于 Obsidian v1.4.x 与 Dataview v0.5.x · 兼容移动端与桌面端 · 开源免费
        </div>
    </div>
</body>
</html> 