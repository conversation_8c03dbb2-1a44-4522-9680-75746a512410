# 测试库项目环境变量模板
# 复制此文件为 .env 并填入实际值
# 注意: .env 文件包含敏感信息，不应提交到版本控制

# ===== Obsidian 配置 =====
# Obsidian Local REST API 配置
OBSIDIAN_API_KEY=your_obsidian_api_key_here
OBSIDIAN_HOST=https://127.0.0.1:27124
OBSIDIAN_PORT=27124
OBSIDIAN_VAULT_PATH=/path/to/your/obsidian/vault

# ===== AI 服务配置 =====
# OpenAI API 配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ORG_ID=your_openai_org_id_here

# Replicate API 配置
REPLICATE_API_TOKEN=your_replicate_api_token_here

# Together AI 配置
TOGETHER_API_KEY=your_together_api_key_here

# Claude API 配置 (如果使用)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# ===== MCP 服务配置 =====
# MCP 服务端口配置
MCP_SERVER_PORT=3001
MCP_SERVER_HOST=localhost

# MCP 调试模式
DEBUG=false
MCP_DEBUG=false

# ===== 应用配置 =====
# 应用环境
NODE_ENV=development
PYTHON_ENV=development

# 日志级别
LOG_LEVEL=INFO

# 数据目录
DATA_DIR=./data
OUTPUT_DIR=./output
TEMP_DIR=./temp

# ===== 推广图生成配置 =====
# 默认图片设置
DEFAULT_IMAGE_WIDTH=1920
DEFAULT_IMAGE_HEIGHT=1080
DEFAULT_IMAGE_QUALITY=95
DEFAULT_IMAGE_FORMAT=jpg

# 浏览器配置
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000

# ===== 数据库配置 =====
# SQLite 数据库路径
DATABASE_URL=sqlite:///./data/app.db

# ===== 安全配置 =====
# 会话密钥 (生产环境请使用强密钥)
SECRET_KEY=your_secret_key_here

# CORS 配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# ===== 第三方服务配置 =====
# GitHub 配置 (如果需要)
GITHUB_TOKEN=your_github_token_here
GITHUB_USERNAME=your_github_username

# 邮件服务配置 (如果需要)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password

# ===== 性能配置 =====
# 并发设置
MAX_WORKERS=4
BATCH_SIZE=10

# 缓存配置
CACHE_TTL=3600
CACHE_SIZE=1000

# ===== 开发工具配置 =====
# 热重载
HOT_RELOAD=true

# 自动格式化
AUTO_FORMAT=true

# 测试配置
TEST_DATABASE_URL=sqlite:///./data/test.db

# ===== 使用说明 =====
# 1. 复制此文件为 .env
# 2. 填入实际的API密钥和配置值
# 3. 确保 .env 文件在 .gitignore 中
# 4. 重启应用以加载新的环境变量

# ===== 获取API密钥的方法 =====
# Obsidian API Key: 
#   1. 安装 Local REST API 插件
#   2. 在插件设置中生成 API Key
#
# OpenAI API Key:
#   1. 访问 https://platform.openai.com/api-keys
#   2. 创建新的 API Key
#
# Replicate API Token:
#   1. 访问 https://replicate.com/account/api-tokens
#   2. 创建新的 API Token
#
# Together AI API Key:
#   1. 访问 https://api.together.xyz/settings/api-keys
#   2. 创建新的 API Key
