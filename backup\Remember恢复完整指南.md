# Remember 全局记忆恢复完整指南

> 📅 创建时间：2025-08-14 星期四
> 🎯 目的：账号问题后快速恢复 Augment Agent Remember 全局记忆
> ⚡ 特点：逐步恢复，分批验证，确保完整性

## 🚨 恢复场景

### 常见情况
- **账号丢失**：无法访问原有账号
- **账号重置**：账号可用但记忆清空
- **服务迁移**：切换到新的 Augment 服务
- **数据损坏**：部分记忆内容丢失

## 🔄 恢复流程

### 第一步：核心工作偏好（必须优先恢复）

```markdown
remember: 用户偏好中文交流，生成总结性Markdown文档，不生成测试脚本，需要时协助编译和运行
```

```markdown
remember: 用户关注数据安全性，需要建立记忆备份和恢复机制，防止账号问题导致的数据丢失
```

**验证方法**：测试中文对话和文档生成偏好是否生效

### 第二步：翻译规则模板

```markdown
remember: 翻译规则模板：当用户说"翻译 XXX文件"时自动应用规则：1)仅翻译英文文本保持代码片段技术术语文件路径命令行指令YAML元数据不变 2)确保翻译准确自然流畅符合中文表达习惯 3)保持完整Markdown格式结构 4)保存为原文件名_zh.md格式到当前工作目录 5)工作流程读取原文件→翻译→保存→确认完成
```

**验证方法**：测试翻译功能是否按规则执行

### 第三步：对话导出约定

```markdown
remember: 对话导出约定：导出对话=完整原始对话记录按界面显示格式包含User/Assistant/思考过程/工具调用等所有内容，导出对话：主题=结构化整理版使用conversation-template.md模板，保存重要对话=深度分析版包含价值分析和经验总结存储到important文件夹
```

**验证方法**：测试对话导出功能是否正确分类

### 第四步：MCP工具配置经验

```markdown
remember: MCP工具优先使用顺序：寸止MCP用户反馈交互和项目记忆管理，Context7查询最新库文档示例，Playwright浏览器自动化操作，sequential-thinking复杂任务分解与思考，shrimp-task-manager任务规划和管理，其他MCP服务根据需要调用
```

```markdown
remember: 寸止MCP交互规则：需求不明确时使用寸止询问澄清提供预定义选项，有多个方案时使用寸止询问而非自作主张，即将完成请求前必须调用寸止请求反馈，在没有明确通过寸止询问并得到可以完成任务结束时禁止主动结束对话请求
```

**验证方法**：测试MCP工具调用顺序和寸止交互是否正常

### 第五步：知识重构原则

```markdown
remember: 知识重构原则：模式识别大于细节记忆提取规律和模式而非堆砌细节，生动形象大于抽象概念用具体可视化方式表达抽象内容，情感共鸣大于理性说服触动情感让知识有温度，连接已知大于全新信息与现有知识体系建立联系，可行洞察大于纯粹知识提供可执行的行动指导
```

**验证方法**：测试知识处理是否符合重构原则

### 第六步：任务管理和项目偏好

```markdown
remember: Obsidian知识管理系统偏好：问题导向的结构化框架，文档属性和标签体系，可视化仪表盘设计，定期回顾机制，简约任务管理系统三栏布局，项目管理系统单一状态属性和彩色图标显示
```

```markdown
remember: 项目路径标准化约定：任务计划存储在./issues/目录，任务复盘存储在./rewind/目录，内容存储在./notes/目录，推广图输出到./cursor_projects/Ob/目录，图片导出到./cursor_projects/pic/images/目录
```

**验证方法**：测试项目管理和路径约定是否生效

### 第七步：技术配置和CLI工具

```markdown
remember: PowerShell和CLI工具配置偏好：CLI工具持久化配置使用Environment.SetEnvironmentVariable设置用户级环境变量，PowerShell应用安装使用Add-AppPackage命令，技术工具安装遵循标准化流程分析文档检查完整性验证要求按顺序执行验证结果提供优化建议
```

**验证方法**：测试技术配置建议是否符合偏好

## 📋 恢复验证清单

### 基础功能验证
- [ ] 中文交流正常
- [ ] 文档生成偏好正确（生成总结性Markdown，不生成测试脚本）
- [ ] 编译运行协助功能可用
- [ ] 翻译规则模板生效

### 交互功能验证
- [ ] 寸止MCP交互正常
- [ ] 对话导出功能正确
- [ ] MCP工具调用顺序正确
- [ ] 项目路径约定生效

### 高级功能验证
- [ ] 知识重构原则应用
- [ ] 任务管理偏好生效
- [ ] 技术配置建议正确
- [ ] 数据安全意识体现

## 🔧 快速恢复脚本

### 一键恢复命令（复制粘贴执行）

```markdown
remember: 用户偏好中文交流，生成总结性Markdown文档，不生成测试脚本，需要时协助编译和运行

remember: 翻译规则模板：当用户说"翻译 XXX文件"时自动应用规则：1)仅翻译英文文本保持代码片段技术术语文件路径命令行指令YAML元数据不变 2)确保翻译准确自然流畅符合中文表达习惯 3)保持完整Markdown格式结构 4)保存为原文件名_zh.md格式到当前工作目录 5)工作流程读取原文件→翻译→保存→确认完成

remember: 对话导出约定：导出对话=完整原始对话记录，导出对话：主题=结构化整理版，保存重要对话=深度分析版

remember: MCP工具优先使用顺序：寸止MCP用户反馈交互，Context7查询库文档，Playwright浏览器自动化，sequential-thinking复杂任务分解，shrimp-task-manager任务规划管理

remember: 知识重构原则：模式识别>细节记忆，生动形象>抽象概念，情感共鸣>理性说服，连接已知>全新信息，可行洞察>纯粹知识

remember: 用户关注数据安全性，已建立完整的记忆备份系统包含三层架构保护自动化脚本手动备份命令桌面快捷方式和详细恢复机制确保数据安全
```

## ⚠️ 注意事项

### 恢复顺序
1. **先恢复核心偏好**：确保基础交流正常
2. **再恢复功能规则**：逐步添加具体功能
3. **最后恢复高级特性**：完善系统配置

### 分批执行
- **不要一次性**：避免同时添加所有记忆
- **逐条验证**：每添加一条记忆都要测试
- **出错回滚**：如有问题立即停止并检查

### 备用方案
如果 remember 功能也有问题：
1. 将记忆内容写入项目的 `.augment-guidelines` 文件
2. 使用寸止MCP的项目记忆功能
3. 创建临时的工作偏好文档

## 🔄 定期更新

### 备份更新
- **每次重要变更后**：更新恢复指南
- **每月检查**：验证恢复命令的有效性
- **版本控制**：使用Git跟踪恢复指南的变更

### 测试恢复
- **模拟测试**：定期在测试环境验证恢复流程
- **文档更新**：根据测试结果更新恢复指南
- **经验积累**：记录恢复过程中的问题和解决方案

---

## 💡 使用建议

1. **保存此文档**：将恢复指南保存到多个位置
2. **定期练习**：熟悉恢复流程和命令
3. **及时更新**：记忆内容变更后及时更新恢复指南
4. **多重备份**：同时使用本地文件和云存储保存

**记住**：恢复是备份的最终目的，定期验证恢复流程的可用性！
