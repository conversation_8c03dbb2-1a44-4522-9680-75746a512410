import os
import sys
import asyncio
from playwright.async_api import async_playwright
import time
from PIL import Image

async def generate_promo_image():
    """使用Playwright生成番茄钟推广图片"""
    
    # 创建输出目录
    output_dir = r"C:\Users\<USER>\Desktop\测试库\cursor_projects\Ob"
    os.makedirs(output_dir, exist_ok=True)
    
    # HTML文件和输出图片的路径
    html_path = os.path.join(output_dir, "pomodoro_promo.html")
    temp_output_path = os.path.join(output_dir, "pomodoro_promo_temp.jpg")
    final_output_path = os.path.join(output_dir, "pomodoro_promo.jpg")
    
    # 确保HTML文件存在
    if not os.path.exists(html_path):
        print(f"错误：HTML文件不存在于路径 {html_path}")
        return False
    
    print("开始生成番茄钟推广图片...")
    
    try:
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(headless=True)
            
            # 创建一个略大的视口以确保内容不被裁剪
            context = await browser.new_context(
                viewport={"width": 1200, "height": 1800}
            )
            
            # 创建页面并导航到HTML文件
            page = await context.new_page()
            file_url = f"file://{html_path}"
            await page.goto(file_url)
            
            # 等待页面完全加载
            await page.wait_for_load_state("networkidle")
            
            # 调整页面尺寸
            await page.evaluate("""() => {
                document.body.style.width = '1200px';
                document.body.style.height = '1800px';
            }""")
            
            # 添加额外延迟确保渲染完成
            await asyncio.sleep(2)
            
            # 截图并保存为临时JPG
            await page.screenshot(path=temp_output_path, type="jpeg", quality=95, full_page=True)
            
            # 关闭浏览器
            await browser.close()
            
        # 使用PIL调整图片到精确尺寸
        with Image.open(temp_output_path) as img:
            img_resized = img.resize((1200, 1800), Image.LANCZOS)
            img_resized.save(final_output_path, "JPEG", quality=95)
        
        # 删除临时文件
        if os.path.exists(temp_output_path):
            os.remove(temp_output_path)
            
        print(f"推广图片已成功生成并保存至：{final_output_path}")
        
        # 验证图片尺寸
        with Image.open(final_output_path) as final_img:
            width, height = final_img.size
            print(f"最终图片尺寸: {width}x{height} 像素")
            if width != 1200 or height != 1800:
                print("警告：图片尺寸与目标不符")
            else:
                print("图片尺寸正确：1200x1800像素")
                
        return True
    
    except Exception as e:
        print(f"生成图片时发生错误：{e}")
        return False

if __name__ == "__main__":
    asyncio.run(generate_promo_image()) 