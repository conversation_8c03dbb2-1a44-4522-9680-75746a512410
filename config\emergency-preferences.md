# Augment Agent 应急工作偏好配置

> 📅 创建时间：2025-08-14 星期四
> 🎯 用途：Remember 功能故障时的备用配置
> ⚡ 特点：快速应用，完整覆盖核心偏好

## 🚨 使用场景

当 Augment Agent 的 Remember 功能无法正常工作时，使用此配置文件作为备用方案。

### 应急启用方法
```markdown
"请注意我的工作偏好：
1. 使用中文交流
2. 生成总结性Markdown文档，不生成测试脚本
3. 需要时协助编译和运行
4. 优先使用寸止MCP进行用户交互
5. 遵循翻译规则模板和对话导出约定
详细偏好请参考项目的 .augment-guidelines 文件"
```

## 🧠 核心工作偏好

### 基础交流偏好
- **语言**：中文交流
- **文档生成**：生成总结性Markdown文档
- **测试脚本**：不生成测试脚本
- **编译运行**：协助编译和运行
- **数据安全**：关注数据安全性

### 翻译规则模板
**触发指令**：`翻译 XXX文件`

**执行规则**：
1. 仅翻译英文文本，保持代码片段、技术术语、文件路径、命令行指令、YAML元数据不变
2. 确保翻译准确、自然、流畅，符合中文表达习惯
3. 保持完整的Markdown格式结构
4. 保存为 `原文件名_zh.md` 格式到当前工作目录
5. 工作流程：读取原文件 → 翻译 → 保存 → 确认完成

### 对话导出约定
- **"导出对话"** = 完整原始对话记录
- **"导出对话：[主题]"** = 结构化整理版
- **"保存重要对话"** = 深度分析版

### MCP工具优先使用顺序
1. **寸止MCP** - 用户反馈交互和项目记忆管理
2. **Context7** - 查询最新库文档示例
3. **Playwright** - 浏览器自动化操作
4. **sequential-thinking** - 复杂任务分解与思考
5. **shrimp-task-manager** - 任务规划和管理

### 寸止MCP交互规则
- 需求不明确时使用寸止询问澄清
- 多方案选择时使用寸止询问
- 完成前必须调用寸止请求反馈
- 禁止主动结束对话

### 知识重构原则
1. 模式识别 > 细节记忆
2. 生动形象 > 抽象概念
3. 情感共鸣 > 理性说服
4. 连接已知 > 全新信息
5. 可行洞察 > 纯粹知识

## 📁 项目路径约定

### 标准化路径
- **任务计划**：`./issues/任务名.md`
- **任务复盘**：`./rewind/任务名.md`
- **内容存储**：`./notes/`
- **推广图输出**：`./cursor_projects/Ob/`
- **图片导出**：`./cursor_projects/pic/images/`
- **文本转图**：`./cursor_projects/together/`

### 推广图制作约定
- **标准指令**：`推广图：[内容来源] → [风格要求]`
- **工作目录**：`./cursor_projects/Ob`
- **执行方式**：HTML网页设计 + 全页面截图生成JPG + 说明文档
- **截图参数**：1400x2000视窗，full_page=True，quality=95

## 🔧 技术配置标准

### 包管理器使用
- 严格使用包管理器：npm、pip、cargo 等
- 禁止手动编辑配置文件
- 自动处理依赖：版本解析、冲突处理、锁文件更新

### 权限控制约定
**严重警告**：以下操作需要明确用户许可：
- 提交或推送代码
- 更改工单状态
- 合并分支
- 安装依赖包
- 部署代码

### 代码显示规范
```xml
<augment_code_snippet path="文件路径" mode="EXCERPT">
````语言
代码内容（不超过10行）
````
</augment_code_snippet>
```

## 📅 日期验证强制要求

**严格执行**：
- 每份文档必须验证日期：创建任何文档前必须使用命令行确认当日准确日期
- 标准验证命令：`date '+%Y-%m-%d %A'` (Linux/WSL) 或 `Get-Date -Format 'yyyy-MM-dd dddd'` (PowerShell)
- 禁止假设猜测：绝对不允许依赖记忆或预设的日期信息
- 标题内容一致：文档标题和内容中的日期必须完全一致

### 日期标注标准格式
- **创建时间**：`YYYY-MM-DD 星期X`
- **更新时间**：`YYYY-MM-DD`
- **文件命名**：`项目名-YYYYMMDD.md`

## 🔄 困难恢复机制

- 发现陷入循环或重复调用相同工具时，主动向用户求助
- 不要在兔子洞里越陷越深
- 建议编写和运行测试来验证代码质量

## 📝 使用说明

### 应急启用步骤
1. **复制标准声明**：使用上方的应急启用方法
2. **粘贴到对话**：在新对话开始时粘贴声明
3. **验证效果**：测试核心功能是否按偏好执行
4. **持续提醒**：必要时在对话中重复提醒偏好

### 维护更新
- **同步更新**：Remember 功能恢复后，将新偏好同步到此文件
- **定期检查**：每月检查配置是否与实际需求一致
- **版本控制**：使用 Git 跟踪配置文件的变更

### 恢复策略
- **Remember 恢复后**：立即迁移回主要方案
- **长期使用**：如果 Remember 长期不可用，优化此配置作为主要方案

---

## ⚠️ 重要提醒

此配置文件是 Remember 功能的完整备份，包含所有核心工作偏好。在 Remember 功能故障期间，请严格按照此配置与 Augment Agent 协作。

**记住**：备用方案的目标是确保工作连续性，而不是完全替代 Remember 功能！
