import os
from PIL import Image

def verify_image(image_path):
    """验证图片尺寸和格式"""
    if not os.path.exists(image_path):
        print(f"错误：图片不存在于路径 {image_path}")
        return False
    
    try:
        with Image.open(image_path) as img:
            width, height = img.size
            format = img.format
            
            print(f"图片路径: {image_path}")
            print(f"图片尺寸: {width}x{height} 像素")
            print(f"图片格式: {format}")
            print(f"文件大小: {os.path.getsize(image_path) / 1024:.2f} KB")
            
            if width == 1200 and height == 1800 and format == "JPEG":
                print("✓ 图片符合要求：1200x1800像素，JPG格式")
                return True
            else:
                print("✗ 图片不符合要求")
                print(f"  - 当前尺寸: {width}x{height}，要求尺寸: 1200x1800")
                print(f"  - 当前格式: {format}，要求格式: JPEG")
                return False
    
    except Exception as e:
        print(f"验证图片时发生错误：{e}")
        return False

if __name__ == "__main__":
    image_path = r"C:\Users\<USER>\Desktop\测试库\cursor_projects\Ob\pomodoro_promo.jpg"
    verify_image(image_path) 