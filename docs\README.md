# 📚 项目文档

> 测试库项目的完整文档索引

## 📖 文档导航

### 🚀 快速开始
- [项目概述](../README.md) - 项目介绍和快速开始指南
- [开发指南](DEVELOPMENT.md) - 开发环境设置和开发流程
- [贡献指南](CONTRIBUTING.md) - 如何为项目做贡献

### 🛠️ 开发文档
- [模板开发](template-development.md) - 模板开发指南
- [推广图指南](promotional-image-guide.md) - 推广图制作指南

### 📋 项目管理
- [项目现状分析](项目现状分析报告.md) - 当前项目状态分析
- [结构优化方案](项目结构优化方案.md) - 项目结构改进方案
- [README文档结构设计](README文档结构设计方案.md) - 文档结构设计方案

### 🔧 技术文档
- [MCP配置指南](../config/mcp/) - MCP服务配置说明
- [工具使用指南](../tools/) - 开发工具使用说明
- [脚本参考](../scripts/) - 脚本功能参考
- [输出文件管理](../output/) - 输出文件组织说明

### 📁 目录结构

```
docs/
├── README.md                      # 本文档索引
├── DEVELOPMENT.md                 # 开发指南
├── CONTRIBUTING.md                # 贡献指南
├── template-development.md        # 模板开发
├── promotional-image-guide.md     # 推广图指南
├── 项目现状分析报告.md             # 项目分析
├── 项目结构优化方案.md             # 优化方案
├── README文档结构设计方案.md       # 文档结构设计
└── images/                        # 文档图片

项目结构/
├── obsidian-vault/                # Obsidian知识库
├── tools/                         # 开发工具
├── scripts/                       # 脚本文件
├── config/                        # 配置文件
├── output/                        # 输出文件
├── temp/                          # 临时文件
├── docs/                          # 项目文档
└── tests/                         # 测试文件
```

## 🔗 相关链接

- [项目仓库](https://github.com/your-username/测试库)
- [问题反馈](https://github.com/your-username/测试库/issues)
- [讨论区](https://github.com/your-username/测试库/discussions)

## 📝 文档维护

文档最后更新时间：2025-06-28 19:05:10

如果发现文档中的错误或需要补充内容，请提交Issue或Pull Request。
