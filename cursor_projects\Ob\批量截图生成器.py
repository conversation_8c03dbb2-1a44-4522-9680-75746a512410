#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
番茄钟推广图片批量生成器
自动截图生成不同风格的推广图片
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def setup_driver():
    """设置Chrome浏览器驱动"""
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--window-size=1200,1800')
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        print(f"Chrome驱动设置失败: {e}")
        return None

def take_screenshot(driver, html_file, output_file):
    """截图并保存"""
    try:
        # 打开HTML文件
        file_url = f"file:///{html_file.replace(os.sep, '/')}"
        driver.get(file_url)
        
        # 等待页面加载
        time.sleep(3)
        
        # 设置窗口大小
        driver.set_window_size(1200, 1800)
        
        # 截图
        driver.save_screenshot(output_file)
        print(f"✅ 成功生成: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ 截图失败 {output_file}: {e}")
        return False

def main():
    """主函数"""
    base_dir = r"C:\Users\<USER>\Desktop\测试库\cursor_projects\Ob"
    
    # 定义所有风格的文件
    styles = [
        ("番茄钟推广图.html", "番茄钟推广图_深蓝科技风.jpg"),
        ("番茄钟推广图_橙色活力风.html", "番茄钟推广图_橙色活力风.jpg"),
        ("番茄钟推广图_白色商务风.html", "番茄钟推广图_白色商务风.jpg"),
        ("番茄钟推广图_紫色梦幻风.html", "番茄钟推广图_紫色梦幻风.jpg"),
        ("番茄钟推广图_绿色清新风.html", "番茄钟推广图_绿色清新风.jpg"),
    ]
    
    # 设置浏览器驱动
    driver = setup_driver()
    if not driver:
        print("❌ 无法启动浏览器驱动")
        return
    
    print("🎨 开始批量生成番茄钟推广图片...")
    success_count = 0
    
    try:
        for html_file, jpg_file in styles:
            html_path = os.path.join(base_dir, html_file)
            jpg_path = os.path.join(base_dir, jpg_file)
            
            if os.path.exists(html_path):
                if take_screenshot(driver, html_path, jpg_path):
                    success_count += 1
            else:
                print(f"⚠️  HTML文件不存在: {html_path}")
    
    finally:
        driver.quit()
    
    print(f"\n🎉 批量生成完成！成功生成 {success_count}/{len(styles)} 张图片")
    print(f"📁 保存位置: {base_dir}")

if __name__ == "__main__":
    main()
