<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP服务器工具套件</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: #1a1d29;
            color: #ffffff;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 60px 40px;
        }

        .header {
            text-align: center;
            margin-bottom: 80px;
        }

        .main-title {
            font-size: 3.5rem;
            font-weight: 700;
            color: #00ff88;
            margin-bottom: 15px;
            letter-spacing: -1px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #8892b0;
            margin-bottom: 20px;
            font-weight: 400;
        }

        .description {
            font-size: 1rem;
            color: #64748b;
            margin-bottom: 50px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .stats-section {
            display: flex;
            justify-content: center;
            gap: 80px;
            margin-bottom: 80px;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #00ff88;
            margin-bottom: 8px;
            display: block;
        }

        .stat-label {
            font-size: 1rem;
            color: #8892b0;
            font-weight: 500;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .service-card {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 16px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color);
            border-radius: 16px 16px 0 0;
        }

        .service-card:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.05);
            border-color: var(--card-color);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .service-icon {
            font-size: 2.5rem;
            margin-right: 15px;
            color: var(--card-color);
        }

        .service-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #ffffff;
        }

        .service-subtitle {
            font-size: 0.9rem;
            color: #8892b0;
            margin-top: 5px;
        }

        .workflow {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 25px 0;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .workflow-step {
            text-align: center;
            flex: 1;
            position: relative;
        }

        .step-icon {
            width: 50px;
            height: 50px;
            background: var(--card-color);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 1.5rem;
            color: #1a1d29;
            font-weight: 600;
        }

        .step-label {
            font-size: 0.8rem;
            color: #8892b0;
            font-weight: 500;
        }

        .workflow-arrow {
            font-size: 1.2rem;
            color: var(--card-color);
            margin: 0 10px;
        }

        .features-list {
            list-style: none;
            margin-bottom: 20px;
        }

        .features-list li {
            padding: 6px 0;
            color: #cbd5e1;
            font-size: 0.9rem;
            position: relative;
            padding-left: 20px;
        }

        .features-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--card-color);
            font-weight: 600;
        }

        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag {
            background: rgba(255, 255, 255, 0.1);
            color: var(--card-color);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 服务卡片颜色 */
        .card-feedback { --card-color: #00ff88; }
        .card-obsidian { --card-color: #3b82f6; }
        .card-context7 { --card-color: #06b6d4; }
        .card-thinking { --card-color: #8b5cf6; }
        .card-playwright { --card-color: #f97316; }
        .card-replicate { --card-color: #ec4899; }
        .card-together { --card-color: #eab308; }
        .card-shrimp { --card-color: #ef4444; }
        .card-fetch { --card-color: #64748b; }

        @media (max-width: 1024px) {
            .services-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
            }

            .stats-section {
                gap: 50px;
            }
        }

        @media (max-width: 768px) {
            .services-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .stats-section {
                gap: 30px;
            }

            .main-title {
                font-size: 2.5rem;
            }

            .container {
                padding: 40px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">MCP服务器工具套件</h1>
            <p class="subtitle">Model Context Protocol Server Tools</p>
            <p class="description">开放协议标准化AI应用与外部数据源和工具的无缝集成 • 像USB-C一样连接AI模型到各种数据源</p>

            <div class="stats-section">
                <div class="stat-item">
                    <span class="stat-number">9个</span>
                    <span class="stat-label">核心服务</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">98.7%</span>
                    <span class="stat-label">配置成功率</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">50+</span>
                    <span class="stat-label">诊断工具</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">2个</span>
                    <span class="stat-label">支持IDE</span>
                </div>
            </div>
        </div>

        <div class="services-grid">
            <!-- mcp-feedback-enhanced -->
            <div class="service-card card-feedback">
                <div class="card-header">
                    <div class="service-icon">💬</div>
                    <div>
                        <div class="service-title">Interactive Feedback</div>
                        <div class="service-subtitle">用户交互反馈工具</div>
                    </div>
                </div>

                <div class="workflow">
                    <div class="workflow-step">
                        <div class="step-icon">🔍</div>
                        <div class="step-label">系统检测</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">📊</div>
                        <div class="step-label">数据收集</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">🧮</div>
                        <div class="step-label">智能分析</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">💬</div>
                        <div class="step-label">结果输出</div>
                    </div>
                </div>

                <ul class="features-list">
                    <li>实时系统监控和性能诊断</li>
                    <li>环境配置检测和验证</li>
                    <li>用户交互反馈收集</li>
                    <li>自动化问题识别机制</li>
                </ul>

                <div class="tags">
                    <span class="tag">系统诊断</span>
                    <span class="tag">性能监控</span>
                    <span class="tag">环境检测</span>
                </div>
            </div>

            <!-- mcp-obsidian -->
            <div class="service-card card-obsidian">
                <div class="card-header">
                    <div class="service-icon">📚</div>
                    <div>
                        <div class="service-title">Obsidian Integration</div>
                        <div class="service-subtitle">知识库集成工具</div>
                    </div>
                </div>

                <div class="workflow">
                    <div class="workflow-step">
                        <div class="step-icon">🔗</div>
                        <div class="step-label">连接知识库</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">🔍</div>
                        <div class="step-label">内容搜索</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">📖</div>
                        <div class="step-label">笔记读取</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">✏️</div>
                        <div class="step-label">创建编辑</div>
                    </div>
                </div>

                <ul class="features-list">
                    <li>Obsidian知识库无缝集成</li>
                    <li>全文搜索和内容检索</li>
                    <li>笔记创建和编辑管理</li>
                    <li>支持Markdown格式处理</li>
                </ul>

                <div class="tags">
                    <span class="tag">知识管理</span>
                    <span class="tag">笔记操作</span>
                    <span class="tag">全文搜索</span>
                </div>
            </div>

            <!-- context7 -->
            <div class="service-card card-context7">
                <div class="card-header">
                    <div class="service-icon">📖</div>
                    <div>
                        <div class="service-title">Context7 Docs</div>
                        <div class="service-subtitle">技术文档查询</div>
                    </div>
                </div>

                <div class="workflow">
                    <div class="workflow-step">
                        <div class="step-icon">❓</div>
                        <div class="step-label">查询请求</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">🔎</div>
                        <div class="step-label">文档检索</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">📄</div>
                        <div class="step-label">内容解析</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">📤</div>
                        <div class="step-label">结果返回</div>
                    </div>
                </div>

                <ul class="features-list">
                    <li>最新技术文档实时查询</li>
                    <li>API参考和代码示例</li>
                    <li>多语言技术资料支持</li>
                    <li>智能内容推荐机制</li>
                </ul>

                <div class="tags">
                    <span class="tag">文档查询</span>
                    <span class="tag">API参考</span>
                    <span class="tag">代码示例</span>
                </div>
            </div>

            <!-- sequential-thinking -->
            <div class="service-card card-thinking">
                <div class="card-header">
                    <div class="service-icon">🧠</div>
                    <div>
                        <div class="service-title">Sequential Thinking</div>
                        <div class="service-subtitle">序列化思维分析</div>
                    </div>
                </div>

                <div class="workflow">
                    <div class="workflow-step">
                        <div class="step-icon">🤔</div>
                        <div class="step-label">问题分析</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">🧩</div>
                        <div class="step-label">步骤分解</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">⚡</div>
                        <div class="step-label">逻辑推理</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">💡</div>
                        <div class="step-label">方案输出</div>
                    </div>
                </div>

                <ul class="features-list">
                    <li>结构化思维分析框架</li>
                    <li>复杂问题逐步分解</li>
                    <li>逻辑推理和决策支持</li>
                    <li>可追溯的思考过程</li>
                </ul>

                <div class="tags">
                    <span class="tag">思维分析</span>
                    <span class="tag">逻辑推理</span>
                    <span class="tag">问题分解</span>
                </div>
            </div>

            <!-- playwright -->
            <div class="service-card card-playwright">
                <div class="card-header">
                    <div class="service-icon">🌐</div>
                    <div>
                        <div class="service-title">Playwright Automation</div>
                        <div class="service-subtitle">浏览器自动化</div>
                    </div>
                </div>

                <div class="workflow">
                    <div class="workflow-step">
                        <div class="step-icon">🚀</div>
                        <div class="step-label">启动浏览器</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">👆</div>
                        <div class="step-label">页面操作</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">🕷️</div>
                        <div class="step-label">数据抓取</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">⚙️</div>
                        <div class="step-label">结果处理</div>
                    </div>
                </div>

                <ul class="features-list">
                    <li>跨浏览器自动化操作</li>
                    <li>网页截图和表单填写</li>
                    <li>数据抓取和内容提取</li>
                    <li>支持现代Web应用测试</li>
                </ul>

                <div class="tags">
                    <span class="tag">浏览器自动化</span>
                    <span class="tag">数据抓取</span>
                    <span class="tag">网页测试</span>
                </div>
            </div>

            <!-- replicate-flux -->
            <div class="service-card card-replicate">
                <div class="card-header">
                    <div class="service-icon">🎨</div>
                    <div>
                        <div class="service-title">Replicate Flux</div>
                        <div class="service-subtitle">AI图像生成</div>
                    </div>
                </div>

                <div class="workflow">
                    <div class="workflow-step">
                        <div class="step-icon">💭</div>
                        <div class="step-label">提示输入</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">🤖</div>
                        <div class="step-label">AI处理</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">🎨</div>
                        <div class="step-label">图像生成</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">📸</div>
                        <div class="step-label">格式输出</div>
                    </div>
                </div>

                <ul class="features-list">
                    <li>高质量AI图像生成</li>
                    <li>多种艺术风格支持</li>
                    <li>批量处理和API集成</li>
                    <li>可控的生成参数调节</li>
                </ul>

                <div class="tags">
                    <span class="tag">AI图像</span>
                    <span class="tag">艺术创作</span>
                    <span class="tag">批量生成</span>
                </div>
            </div>

            <!-- together-image-gen -->
            <div class="service-card card-together">
                <div class="card-header">
                    <div class="service-icon">🖼️</div>
                    <div>
                        <div class="service-title">Together Image Gen</div>
                        <div class="service-subtitle">图像创作工具</div>
                    </div>
                </div>

                <div class="workflow">
                    <div class="workflow-step">
                        <div class="step-icon">📝</div>
                        <div class="step-label">创作请求</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">🎭</div>
                        <div class="step-label">风格处理</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">⚡</div>
                        <div class="step-label">快速生成</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">📦</div>
                        <div class="step-label">批量输出</div>
                    </div>
                </div>

                <ul class="features-list">
                    <li>快速图像创作和编辑</li>
                    <li>实时风格迁移处理</li>
                    <li>批量图像处理能力</li>
                    <li>多格式输出支持</li>
                </ul>

                <div class="tags">
                    <span class="tag">快速创作</span>
                    <span class="tag">风格迁移</span>
                    <span class="tag">批量处理</span>
                </div>
            </div>

            <!-- shrimp-task-manager -->
            <div class="service-card card-shrimp">
                <div class="card-header">
                    <div class="service-icon">📋</div>
                    <div>
                        <div class="service-title">Shrimp Task Manager</div>
                        <div class="service-subtitle">智能任务管理</div>
                    </div>
                </div>

                <div class="workflow">
                    <div class="workflow-step">
                        <div class="step-icon">📥</div>
                        <div class="step-label">任务输入</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">🔧</div>
                        <div class="step-label">智能分解</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">📋</div>
                        <div class="step-label">规划执行</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">📈</div>
                        <div class="step-label">进度跟踪</div>
                    </div>
                </div>

                <ul class="features-list">
                    <li>智能任务规划和分解</li>
                    <li>项目管理和工作流优化</li>
                    <li>实时进度跟踪监控</li>
                    <li>团队协作支持功能</li>
                </ul>

                <div class="tags">
                    <span class="tag">任务管理</span>
                    <span class="tag">项目规划</span>
                    <span class="tag">进度跟踪</span>
                </div>
            </div>

            <!-- fetch -->
            <div class="service-card card-fetch">
                <div class="card-header">
                    <div class="service-icon">🌍</div>
                    <div>
                        <div class="service-title">Fetch Network</div>
                        <div class="service-subtitle">网络数据获取</div>
                    </div>
                </div>

                <div class="workflow">
                    <div class="workflow-step">
                        <div class="step-icon">🌐</div>
                        <div class="step-label">URL请求</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">📡</div>
                        <div class="step-label">内容获取</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">🔍</div>
                        <div class="step-label">数据解析</div>
                    </div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">
                        <div class="step-icon">🔄</div>
                        <div class="step-label">格式转换</div>
                    </div>
                </div>

                <ul class="features-list">
                    <li>网页内容获取和解析</li>
                    <li>API数据调用和处理</li>
                    <li>多种数据格式转换</li>
                    <li>错误处理和重试机制</li>
                </ul>

                <div class="tags">
                    <span class="tag">网络请求</span>
                    <span class="tag">数据获取</span>
                    <span class="tag">格式转换</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
