import os
from html2image import Html2Image

def generate_promo_image(html_file, output_file):
    """
    使用html2image将HTML文件转换为PNG图片
    
    Args:
        html_file: HTML文件路径
        output_file: 输出PNG文件路径
    """
    # 获取HTML文件的绝对路径
    abs_html_path = os.path.abspath(html_file)
    
    # 输出文件名（不含路径）
    output_filename = os.path.basename(output_file)
    
    # 输出目录
    output_dir = os.path.dirname(os.path.abspath(output_file))
    
    # 创建Html2Image对象
    hti = Html2Image(output_path=output_dir)
    
    try:
        print(f"正在加载HTML文件: {abs_html_path}")
        
        # 设置大小为容器大小再加上一些额外空间，确保完整捕获
        hti.size = (1300, 1100)
        
        # 从文件生成图片
        hti.screenshot(
            url=f"file:///{abs_html_path}",
            save_as=output_filename,
            size=(1300, 1100)  # 明确指定大小
        )
        
        print(f"图片生成成功! 保存到: {output_file}")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    # 设置文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    html_file = os.path.join(current_dir, "obsidian_task_hexagon_promo.html")
    output_file = os.path.join(current_dir, "obsidian_task_hexagon_promo.png")
    
    # 生成推广图片
    generate_promo_image(html_file, output_file) 