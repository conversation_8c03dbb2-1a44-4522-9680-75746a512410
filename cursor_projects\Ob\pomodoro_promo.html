<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>番茄钟推广图片</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            width: 1200px;
            height: 1800px;
            overflow: hidden;
            background: linear-gradient(135deg, #2a0845 0%, #5533ff 50%, #6b46c1 100%);
            position: relative;
        }
        
        .container {
            width: 100%;
            height: 100%;
            padding: 100px 50px;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 10;
        }
        
        /* 背景装饰 */
        .bg-decoration {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            opacity: 0.5;
        }
        
        .grid-lines {
            position: absolute;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
            background-size: 50px 50px;
        }
        
        .circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.05);
            filter: blur(20px);
        }
        
        .circle-1 {
            top: -100px;
            right: -100px;
            width: 600px;
            height: 600px;
            background: radial-gradient(circle, rgba(138, 43, 226, 0.3), rgba(75, 0, 130, 0.1));
        }
        
        .circle-2 {
            bottom: -200px;
            left: -200px;
            width: 800px;
            height: 800px;
            background: radial-gradient(circle, rgba(147, 112, 219, 0.3), rgba(138, 43, 226, 0.1));
        }
        
        /* 标题区域 */
        .header {
            text-align: center;
            margin-bottom: 100px;
            z-index: 10;
        }
        
        .logo {
            width: 150px;
            height: 150px;
            margin: 0 auto 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .logo svg {
            width: 80px;
            height: 80px;
            fill: rgba(255, 255, 255, 0.9);
        }
        
        .title {
            font-size: 80px;
            font-weight: 800;
            margin-bottom: 20px;
            color: white;
            text-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            letter-spacing: 4px;
        }
        
        .subtitle {
            font-size: 32px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 30px;
            letter-spacing: 2px;
        }
        
        /* 功能卡片区域 */
        .features {
            display: flex;
            flex-direction: column;
            gap: 50px;
            width: 90%;
            margin: 0 auto;
            z-index: 10;
        }
        
        .feature-card {
            display: flex;
            align-items: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .feature-icon {
            width: 120px;
            height: 120px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.15);
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 40px;
            flex-shrink: 0;
        }
        
        .feature-icon svg {
            width: 70px;
            height: 70px;
            fill: rgba(255, 255, 255, 0.9);
        }
        
        .feature-content h3 {
            font-size: 36px;
            font-weight: 600;
            color: white;
            margin-bottom: 20px;
        }
        
        .feature-content p {
            font-size: 24px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
        }
        
        /* 数据可视化元素 */
        .data-visualization {
            margin-top: 80px;
            width: 90%;
            display: flex;
            justify-content: center;
            gap: 30px;
            z-index: 10;
        }
        
        .chart {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: calc(33% - 20px);
        }
        
        .chart-title {
            font-size: 24px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 15px;
        }
        
        .chart-placeholder {
            width: 100%;
            height: 200px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        
        /* 线形图 */
        .line-chart .bar {
            position: absolute;
            bottom: 0;
            width: 8px;
            background: linear-gradient(to top, #5533ff, #9370DB);
            border-radius: 4px 4px 0 0;
        }
        
        /* 饼图 */
        .pie-chart .segment {
            position: absolute;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        /* 仪表盘 */
        .gauge-chart .gauge {
            width: 180px;
            height: 90px;
            border-radius: 90px 90px 0 0;
            background: conic-gradient(
                from 180deg at 50% 100%,
                #5533ff 0%,
                #9370DB 40%,
                #b19cd9 70%,
                rgba(255, 255, 255, 0.3) 70%,
                rgba(255, 255, 255, 0.3) 100%
            );
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .gauge-chart .needle {
            position: absolute;
            width: 4px;
            height: 80px;
            background: white;
            bottom: 10px;
            left: 50%;
            transform-origin: bottom center;
            transform: translateX(-50%) rotate(30deg);
        }
        
        /* 页脚信息 */
        .footer {
            position: absolute;
            bottom: 50px;
            text-align: center;
            width: 100%;
            left: 0;
            color: rgba(255, 255, 255, 0.6);
            font-size: 20px;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="bg-decoration">
        <div class="grid-lines"></div>
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
    </div>
    
    <div class="container">
        <div class="header">
            <div class="logo">
                <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,5A7,7 0 0,0 5,12A7,7 0 0,0 12,19A7,7 0 0,0 19,12A7,7 0 0,0 12,5M11,7H12.5V12.25L16.5,14.5L15.75,15.75L11,13V7"></path>
                </svg>
            </div>
            <h1 class="title">番茄钟</h1>
            <p class="subtitle">高效时间管理·数据驱动·智能分析</p>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">
                    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21,16.5C21,16.88 20.79,17.21 20.47,17.38L12.57,21.82C12.41,21.94 12.21,22 12,22C11.79,22 11.59,21.94 11.43,21.82L3.53,17.38C3.21,17.21 3,16.88 3,16.5V7.5C3,7.12 3.21,6.79 3.53,6.62L11.43,2.18C11.59,2.06 11.79,2 12,2C12.21,2 12.41,2.06 12.57,2.18L20.47,6.62C20.79,6.79 21,7.12 21,7.5V16.5M12,4.15L5,8.09V15.91L12,19.85L19,15.91V8.09L12,4.15M12,6.23L16.9,9.06L12,11.89L7.1,9.06L12,6.23M17,14.89L13,17.2V13.62L17,11.31V14.89M11,17.2L7,14.89V11.31L11,13.62V17.2Z"></path>
                    </svg>
                </div>
                <div class="feature-content">
                    <h3>实时数据处理</h3>
                    <p>智能算法即时分析您的工作模式，动态调整建议，提供精确的时间管理洞察，让每一分钟都更有价值。</p>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22,21H2V3H4V19H22V21M19,15H15V11H13V15H9V7H7V15H4V5H19V15M10,10H12V8H10V10Z"></path>
                    </svg>
                </div>
                <div class="feature-content">
                    <h3>多维度可视化</h3>
                    <p>直观图表展示您的专注时段、工作效率和休息模式，通过数据可视化技术，清晰呈现您的时间投资回报。</p>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19M11,7H13V9H11V7M11,11H13V17H11V11Z"></path>
                    </svg>
                </div>
                <div class="feature-content">
                    <h3>自动报告生成</h3>
                    <p>系统根据您的使用习惯，定期生成详细的时间管理分析报告，提供个性化的改进建议，助您不断优化工作流程。</p>
                </div>
            </div>
        </div>
        
        <div class="data-visualization">
            <div class="chart line-chart">
                <div class="chart-title">专注时长趋势</div>
                <div class="chart-placeholder">
                    <div class="bar" style="height: 60%; left: 10%;"></div>
                    <div class="bar" style="height: 80%; left: 25%;"></div>
                    <div class="bar" style="height: 50%; left: 40%;"></div>
                    <div class="bar" style="height: 90%; left: 55%;"></div>
                    <div class="bar" style="height: 70%; left: 70%;"></div>
                    <div class="bar" style="height: 85%; left: 85%;"></div>
                </div>
            </div>
            
            <div class="chart pie-chart">
                <div class="chart-title">时间分配比例</div>
                <div class="chart-placeholder">
                    <div class="segment" style="background: conic-gradient(
                        from 0deg,
                        #5533ff 0%,
                        #5533ff 40%,
                        #9370DB 40%,
                        #9370DB 65%,
                        #b19cd9 65%,
                        #b19cd9 100%
                    );"></div>
                </div>
            </div>
            
            <div class="chart gauge-chart">
                <div class="chart-title">当日完成率</div>
                <div class="chart-placeholder">
                    <div class="gauge"></div>
                    <div class="needle"></div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            番茄钟 · 科技助力专注 · 数据驱动效率
        </div>
    </div>
</body>
</html> 