# 番茄钟推广图JPG生成说明

## 📁 当前状态
- ✅ 已创建6种风格的HTML文件
- ✅ 部分JPG已生成（深蓝科技风、绿色清新风）
- ❌ AI风格JPG需要手动生成

## 🎨 可用的HTML文件
1. `番茄钟推广图.html` - 深蓝科技风 ✅ 已有JPG
2. `番茄钟推广图_橙色活力风.html` - 橙色活力风
3. `番茄钟推广图_白色商务风.html` - 白色商务风  
4. `番茄钟推广图_紫色梦幻风.html` - 紫色梦幻风
5. `番茄钟推广图_绿色清新风.html` - 绿色清新风 ✅ 已有JPG
6. `番茄钟推广图_AI风格.html` - AI风格（参考您的图片）⭐ 推荐

## 📸 手动生成JPG的方法

### 方法1：浏览器截图（推荐）
1. 在浏览器中打开HTML文件
2. 按 `F12` 打开开发者工具
3. 按 `Ctrl+Shift+P` 打开命令面板
4. 输入 "screenshot"
5. 选择 "Capture full size screenshot"
6. 自动下载PNG文件，可转换为JPG

### 方法2：浏览器打印
1. 在浏览器中打开HTML文件
2. 按 `Ctrl+P` 打印
3. 选择"另存为PDF"
4. 使用在线工具将PDF转换为JPG

### 方法3：截图工具
1. 调整浏览器窗口为1200x1800像素
2. 使用截图工具（如Snipaste）截取完整页面
3. 保存为JPG格式

## 🌐 快速打开链接
- [深蓝科技风](file:///C:/Users/<USER>/Desktop/测试库/cursor_projects/Ob/番茄钟推广图.html)
- [橙色活力风](file:///C:/Users/<USER>/Desktop/测试库/cursor_projects/Ob/番茄钟推广图_橙色活力风.html)
- [白色商务风](file:///C:/Users/<USER>/Desktop/测试库/cursor_projects/Ob/番茄钟推广图_白色商务风.html)
- [紫色梦幻风](file:///C:/Users/<USER>/Desktop/测试库/cursor_projects/Ob/番茄钟推广图_紫色梦幻风.html)
- [绿色清新风](file:///C:/Users/<USER>/Desktop/测试库/cursor_projects/Ob/番茄钟推广图_绿色清新风.html)
- [AI风格](file:///C:/Users/<USER>/Desktop/测试库/cursor_projects/Ob/番茄钟推广图_AI风格.html) ⭐

## 💡 建议
**AI风格**是最接近您提供参考图片的版本，建议优先使用这个风格！

## 📁 保存位置
请将生成的JPG文件保存到：
`C:\Users\<USER>\Desktop\测试库\cursor_projects\Ob\`
