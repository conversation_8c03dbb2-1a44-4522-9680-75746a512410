{"mcpServers": {"shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "timeout": 600, "env": {"DATA_DIR": "C:\\Users\\<USER>\\Desktop\\测试库\\shrimp-data", "TEMPLATES_USE": "zh", "ENABLE_GUI": "true"}}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "autoApprove": ["interactive_feedback"]}, "fetch": {"command": "python", "args": ["-m", "mcp_server_fetch"], "timeout": 300}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "timeout": 300}, "playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"]}, "mcp-obsidian": {"command": "uvx", "args": ["mcp-obsidian"], "env": {"OBSIDIAN_API_KEY": "a712c31903d2c76f85bbf8acb44c4b5c6a497fbcd206557a9102175eebcef6b6", "OBSIDIAN_HOST": "https://127.0.0.1:27124/"}}, "chatlog": {"url": "http://127.0.0.1:5030/sse"}}}