# -*- coding: utf-8 -*-
"""API 路由聚合模块
- 注册各功能子路由：/health, /import, /preprocess, /synthesize, /cache/status, /progress/*, /bookmark/*, /settings/*
"""
from fastapi import APIRouter

from .import_routes import router as import_router
from .preprocess_routes import router as preprocess_router
from .synthesize_routes import router as synthesize_router
from .cache_routes import router as cache_router
from .progress_routes import router as progress_router
from .settings_routes import router as settings_router

router = APIRouter()


@router.get("/health", tags=["health"])  # 基础健康检查
async def health():
    """返回服务健康状态。"""
    return {"status": "ok"}


# 对外暴露的统一路由对象
api_router = APIRouter()
api_router.include_router(router)
api_router.include_router(import_router)
api_router.include_router(preprocess_router)
api_router.include_router(synthesize_router)
api_router.include_router(cache_router)
api_router.include_router(progress_router)
api_router.include_router(settings_router)

