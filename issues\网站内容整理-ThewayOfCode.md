# The Way of Code 网站内容整理任务

## 任务背景
将 https://www.thewayofcode.com/ 网站的所有内容自动浏览并整理成中英文两份Markdown格式文档。

## 整体计划
采用系统性页面爬取法：
1. **网站初始访问与结构分析**
   - 访问主页并确认所有主要导航链接
   - 分析页面结构和布局，确定内容抓取策略

2. **系统性页面爬取**
   - 访问并抓取每个导航菜单链接（About, Contact, Posts等）
   - 获取每个页面的标题、内容和相关元素
   - 对于博客文章，获取完整文章内容

3. **内容整理与翻译**
   - 将英文内容结构化整理为Markdown格式
   - 将英文内容翻译成中文，保持相同的结构

4. **文件生成**
   - 生成英文Markdown文档：`thewayofcode_en.md`
   - 生成中文Markdown文档：`thewayofcode_cn.md`

## 执行流程
1. 访问主页获取导航菜单项
2. 创建导航菜单项的列表
3. 对每个菜单项依次访问并提取内容
4. 特别处理博客文章页面
5. 整理所有英文内容
6. 翻译并整理中文内容
7. 生成最终文档 