# 📸 文档图片目录

> 存放项目文档中使用的图片资源

## 📁 目录结构

```
docs/images/
├── README.md                    # 本说明文档
├── project-dashboard.png        # 项目仪表盘截图
├── mcp-integration.png          # MCP工具集成界面
├── promotional-generator.png    # 推广图生成系统
├── energy-management.png        # 精力管理系统
├── screenshots/                 # 功能截图
├── diagrams/                    # 架构图和流程图
└── logos/                       # 项目标志和图标
```

## 📝 图片规范

### 🖼️ 文件格式
- **截图**：PNG格式，保持原始分辨率
- **图标**：SVG格式优先，PNG作为备选
- **图表**：SVG格式，便于缩放和编辑

### 📏 尺寸规范
- **横幅图片**：1200x600px
- **功能截图**：1024x768px或实际尺寸
- **图标**：64x64px, 128x128px, 256x256px

### 📝 命名规范
- 使用英文小写字母和连字符
- 描述性命名，如：`project-dashboard-overview.png`
- 版本号后缀，如：`logo-v2.svg`

## 🔗 图片引用

在Markdown文档中引用图片：

```markdown
![图片描述](docs/images/图片文件名.png)
```

## 📋 图片清单

### 🎯 核心功能截图
- [ ] project-dashboard.png - 项目仪表盘
- [ ] mcp-integration.png - MCP工具集成
- [ ] promotional-generator.png - 推广图生成
- [ ] energy-management.png - 精力管理

### 🎨 设计资源
- [ ] logo.svg - 项目标志
- [ ] favicon.ico - 网站图标
- [ ] banner.png - 项目横幅

### 📊 架构图表
- [ ] system-architecture.svg - 系统架构图
- [ ] workflow-diagram.svg - 工作流程图
- [ ] data-flow.svg - 数据流图

## 📝 维护说明

1. **添加新图片**时，请更新本文档的图片清单
2. **删除图片**前，请检查是否有文档引用
3. **更新图片**时，保持文件名不变，避免链接失效
4. **优化图片**大小，控制文件体积

最后更新时间：2025-06-28
