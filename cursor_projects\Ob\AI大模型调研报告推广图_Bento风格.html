<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI大模型调研报告 2025年6月版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            padding: 40px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .main-title {
            font-size: 48px;
            font-weight: bold;
            background: linear-gradient(45deg, #64b5f6, #42a5f5, #2196f3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 15px;
        }
        
        .subtitle {
            font-size: 20px;
            color: #b0bec5;
            margin-bottom: 20px;
        }
        
        .reliability-badges {
            display: flex;
            justify-content: center;
            gap: 30px;
            font-size: 16px;
        }
        
        .badge {
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .bento-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(4, auto);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(15px);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .card-large {
            grid-column: span 2;
            grid-row: span 2;
        }
        
        .card-medium {
            grid-column: span 2;
        }
        
        .card-small {
            grid-column: span 1;
        }
        
        .card-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 16px;
            color: #64b5f6;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .card-content {
            line-height: 1.6;
        }
        
        .model-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .model-item:last-child {
            border-bottom: none;
        }
        
        .model-name {
            font-weight: bold;
            color: #ffffff;
        }
        
        .model-desc {
            font-size: 14px;
            color: #b0bec5;
        }
        
        .reliability {
            font-size: 18px;
        }
        
        .stars {
            color: #ffc107;
        }
        
        .scenario-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .scenario-item:last-child {
            border-bottom: none;
        }
        
        .scenario-icon {
            font-size: 20px;
        }
        
        .trend-item {
            padding: 8px 0;
            border-left: 3px solid #64b5f6;
            padding-left: 12px;
            margin-bottom: 8px;
        }
        
        .data-item {
            display: flex;
            justify-content: space-between;
            padding: 6px 0;
            font-size: 14px;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            font-size: 14px;
            color: #b0bec5;
        }
        
        .footer-line {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">AI大模型调研报告</h1>
            <p class="subtitle">2025年6月版 | 让外行人也能快速看懂的AI大模型指南</p>
            <div class="reliability-badges">
                <span class="badge">🟢 高可靠性</span>
                <span class="badge">🟡 中等可靠性</span>
                <span class="badge">🔴 低可靠性</span>
            </div>
        </div>
        
        <div class="bento-grid">
            <!-- 国际顶级模型 -->
            <div class="card card-large">
                <h3 class="card-title">🏆 国际顶级模型排行</h3>
                <div class="card-content">
                    <div class="model-item">
                        <div>
                            <div class="model-name">Claude 4 Opus</div>
                            <div class="model-desc">编程能力极强，长时间编码</div>
                        </div>
                        <span class="reliability">🟢</span>
                    </div>
                    <div class="model-item">
                        <div>
                            <div class="model-name">OpenAI o3-pro</div>
                            <div class="model-desc">推理能力顶级，数学测试优秀</div>
                        </div>
                        <span class="reliability">🟢</span>
                    </div>
                    <div class="model-item">
                        <div>
                            <div class="model-name">Claude 4 Sonnet</div>
                            <div class="model-desc">高性能平衡型，编程优秀</div>
                        </div>
                        <span class="reliability">🟢</span>
                    </div>
                    <div class="model-item">
                        <div>
                            <div class="model-name">GPT-4o</div>
                            <div class="model-desc">综合能力最强，多模态处理优秀</div>
                        </div>
                        <span class="reliability">🟢</span>
                    </div>
                </div>
            </div>
            
            <!-- 中国领先模型 -->
            <div class="card card-medium">
                <h3 class="card-title">🇨🇳 中国领先模型</h3>
                <div class="card-content">
                    <div class="model-item">
                        <div>
                            <div class="model-name">Qwen2.5-Max</div>
                            <div class="model-desc">数学编程双冠王</div>
                        </div>
                    </div>
                    <div class="model-item">
                        <div>
                            <div class="model-name">DeepSeek V3</div>
                            <div class="model-desc">开源第一、性价比高</div>
                        </div>
                    </div>
                    <div class="model-item">
                        <div>
                            <div class="model-name">文心一言 4.0</div>
                            <div class="model-desc">中文理解优秀</div>
                        </div>
                    </div>
                    <div class="model-item">
                        <div>
                            <div class="model-name">腾讯混元</div>
                            <div class="model-desc">首次进入全球前15</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 编程能力对比 -->
            <div class="card card-medium">
                <h3 class="card-title">💻 编程能力对比</h3>
                <div class="card-content">
                    <div class="model-item">
                        <div class="model-name">Claude 4 Opus</div>
                        <div><span class="stars">⭐⭐⭐⭐⭐</span> 卓越</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">GPT-4o</div>
                        <div><span class="stars">⭐⭐⭐⭐⭐</span> 优秀</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">Claude 3.7 Sonnet</div>
                        <div><span class="stars">⭐⭐⭐⭐⭐</span> 优秀</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">Qwen2.5-Coder</div>
                        <div><span class="stars">⭐⭐⭐⭐⭐</span> 优秀</div>
                    </div>
                </div>
            </div>
            
            <!-- 推理能力专项 -->
            <div class="card card-medium">
                <h3 class="card-title">🧠 推理能力专项</h3>
                <div class="card-content">
                    <div class="model-item">
                        <div>
                            <div class="model-name">OpenAI o3-pro</div>
                            <div class="model-desc">卓越</div>
                        </div>
                        <span class="reliability">🟢</span>
                    </div>
                    <div class="model-item">
                        <div>
                            <div class="model-name">OpenAI o3</div>
                            <div class="model-desc">卓越</div>
                        </div>
                        <span class="reliability">🟢</span>
                    </div>
                    <div class="model-item">
                        <div>
                            <div class="model-name">Claude 3.7 Sonnet</div>
                            <div class="model-desc">优秀</div>
                        </div>
                        <span class="reliability">🟢</span>
                    </div>
                    <div class="model-item">
                        <div>
                            <div class="model-name">DeepSeek R1</div>
                            <div class="model-desc">优秀</div>
                        </div>
                        <span class="reliability">🟡</span>
                    </div>
                </div>
            </div>
            
            <!-- 应用场景推荐 -->
            <div class="card card-large">
                <h3 class="card-title">🎯 应用场景推荐</h3>
                <div class="card-content">
                    <div class="scenario-item">
                        <span class="scenario-icon">📝</span>
                        <div>
                            <div class="model-name">内容创作</div>
                            <div class="model-desc">Claude 4 Sonnet</div>
                        </div>
                    </div>
                    <div class="scenario-item">
                        <span class="scenario-icon">💻</span>
                        <div>
                            <div class="model-name">编程开发</div>
                            <div class="model-desc">Claude 4 Opus</div>
                        </div>
                    </div>
                    <div class="scenario-item">
                        <span class="scenario-icon">🔢</span>
                        <div>
                            <div class="model-name">数学计算</div>
                            <div class="model-desc">OpenAI o3-pro</div>
                        </div>
                    </div>
                    <div class="scenario-item">
                        <span class="scenario-icon">🧠</span>
                        <div>
                            <div class="model-name">复杂推理</div>
                            <div class="model-desc">Claude 3.7 Sonnet</div>
                        </div>
                    </div>
                    <div class="scenario-item">
                        <span class="scenario-icon">💰</span>
                        <div>
                            <div class="model-name">成本敏感</div>
                            <div class="model-desc">DeepSeek V3</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 权威数据 -->
            <div class="card card-small">
                <h3 class="card-title">📊 权威数据</h3>
                <div class="card-content">
                    <div class="data-item">
                        <span>GPT-4o MMLU</span>
                        <span>90.5%</span>
                    </div>
                    <div class="data-item">
                        <span>编程能力</span>
                        <span>88.7%</span>
                    </div>
                    <div class="data-item">
                        <span>数学推理</span>
                        <span>92.3%</span>
                    </div>
                    <div class="data-item">
                        <span>数据来源</span>
                        <span>Chatbot Arena</span>
                    </div>
                </div>
            </div>
            
            <!-- 2025年趋势 -->
            <div class="card card-medium">
                <h3 class="card-title">🔥 2025年趋势</h3>
                <div class="card-content">
                    <div class="trend-item">推理模型崛起</div>
                    <div class="trend-item">开源追赶闭源</div>
                    <div class="trend-item">中国模型国际化</div>
                    <div class="trend-item">专业化分工明显</div>
                </div>
            </div>
            
            <!-- 成本效益 -->
            <div class="card card-small">
                <h3 class="card-title">💰 成本效益</h3>
                <div class="card-content">
                    <div class="data-item">
                        <span>高端</span>
                        <span>Claude 4系列</span>
                    </div>
                    <div class="data-item">
                        <span>平衡</span>
                        <span>GPT-4o</span>
                    </div>
                    <div class="data-item">
                        <span>经济</span>
                        <span>DeepSeek V3</span>
                    </div>
                    <div class="data-item">
                        <span>轻量</span>
                        <span>Claude 3.5 Haiku</span>
                    </div>
                </div>
            </div>
            
            <!-- 选择建议 -->
            <div class="card card-medium">
                <h3 class="card-title">🛒 选择建议</h3>
                <div class="card-content">
                    <div class="scenario-item">
                        <span class="scenario-icon">👨‍💼</span>
                        <div>
                            <div class="model-name">商务人士</div>
                            <div class="model-desc">Claude 4 + GPT-4o</div>
                        </div>
                    </div>
                    <div class="scenario-item">
                        <span class="scenario-icon">👨‍💻</span>
                        <div>
                            <div class="model-name">程序员</div>
                            <div class="model-desc">Claude 4 Opus</div>
                        </div>
                    </div>
                    <div class="scenario-item">
                        <span class="scenario-icon">🎓</span>
                        <div>
                            <div class="model-name">学生</div>
                            <div class="model-desc">Claude 3.7 + DeepSeek</div>
                        </div>
                    </div>
                    <div class="scenario-item">
                        <span class="scenario-icon">🏢</span>
                        <div>
                            <div class="model-name">企业</div>
                            <div class="model-desc">私有部署+API</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <div class="footer-line">更新时间：2025年6月22日 | 数据来源：Chatbot Arena, HumanEval, MMLU</div>
            <div class="footer-line">调研方法：Playwright浏览器工具 + Sequential-thinking深度分析</div>
        </div>
    </div>
</body>
</html>
