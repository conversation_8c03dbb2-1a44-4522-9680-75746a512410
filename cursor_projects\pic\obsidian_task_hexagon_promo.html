<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obsidian任务管理系统推广图片</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1a1d29 0%, #2a2d3a 100%);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .promo-container {
            width: 1200px;
            height: 1000px;
            position: relative;
            overflow: hidden;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #1a1d29 0%, #2a2d3a 100%);
            padding: 40px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .main-title {
            font-size: 48px;
            font-weight: bold;
            color: #00d4aa;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 212, 170, 0.3);
        }
        
        .subtitle {
            font-size: 22px;
            color: #ffffff;
            margin-bottom: 20px;
        }
        
        .description {
            font-size: 18px;
            color: #a0a3b1;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        /* 六边形布局容器 */
        .hexagon-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-gap: 30px 10px;
            max-width: 1000px;
            margin: 60px auto;
            padding: 0 20px;
        }
        
        /* 第2行和第4行的偏移 */
        .hexagon-container .hex-card:nth-child(3n+2) {
            transform: translateY(55px);
        }
        
        /* 六边形卡片 */
        .hex-card {
            width: 300px;
            height: 260px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 30px;
            position: relative;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            margin: 0 auto;
            border: 1px solid rgba(0, 212, 170, 0.2);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .hex-card::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(45deg, #00d4aa, transparent, #00d4aa);
            border-radius: 16px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .hex-card:hover {
            transform: translateY(-5px);
        }
        
        .hex-card:hover::before {
            opacity: 1;
            animation: rotate 2s linear infinite;
        }
        
        @keyframes rotate {
            0% {
                background-position: 0% 0%;
            }
            100% {
                background-position: 100% 100%;
            }
        }
        
        .hex-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: #00d4aa;
            text-shadow: 0 0 15px rgba(0, 212, 170, 0.4);
        }
        
        .hex-title {
            font-size: 22px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 15px;
        }
        
        .hex-desc {
            font-size: 14px;
            color: #a0a3b1;
            line-height: 1.5;
        }
        
        /* 工作流程 */
        .workflow {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 60px 40px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 20px;
            padding: 20px;
        }
        
        .workflow-step {
            flex: 1;
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .workflow-step:hover {
            background: rgba(0, 212, 170, 0.1);
            transform: scale(1.05);
        }
        
        .step-title {
            font-size: 18px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }
        
        .workflow-arrow {
            color: #00d4aa;
            font-size: 24px;
            margin: 0 -5px;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            color: #8b8e9b;
            font-size: 14px;
        }
        
        /* 装饰元素 */
        .decoration {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(0, 212, 170, 0.2), rgba(0, 212, 170, 0.05));
            filter: blur(30px);
            z-index: -1;
        }
        
        .dec1 {
            width: 400px;
            height: 400px;
            top: -100px;
            left: -100px;
        }
        
        .dec2 {
            width: 300px;
            height: 300px;
            bottom: -50px;
            right: -50px;
        }
    </style>
</head>
<body>
    <div class="promo-container">
        <!-- 装饰元素 -->
        <div class="decoration dec1"></div>
        <div class="decoration dec2"></div>
        
        <!-- 标题区域 -->
        <div class="header">
            <div class="main-title">Obsidian 任务管理系统</div>
            <div class="subtitle">高效规划 · 科学执行 · 数据驱动</div>
            <div class="description">基于 Obsidian + Dataview 构建的全方位任务管理解决方案</div>
        </div>
        
        <!-- 六边形功能模块展示 -->
        <div class="hexagon-container">
            <!-- 功能模块1: 任务统计卡片 -->
            <div class="hex-card">
                <div class="hex-icon">📊</div>
                <div class="hex-title">任务统计卡片</div>
                <div class="hex-desc">七彩卡片直观展示任务分布状态，包括今天、明天、本周、未来及逾期任务，让您对所有任务一目了然</div>
            </div>
            
            <!-- 功能模块2: 番茄钟管理 -->
            <div class="hex-card">
                <div class="hex-icon">🍅</div>
                <div class="hex-title">番茄钟管理</div>
                <div class="hex-desc">设定每日番茄钟目标，追踪实际完成情况，系统自动计算完成率并生成统计分析，优化时间管理效率</div>
            </div>
            
            <!-- 功能模块3: 项目任务分布 -->
            <div class="hex-card">
                <div class="hex-icon">📁</div>
                <div class="hex-title">项目任务分布</div>
                <div class="hex-desc">智能识别项目文件和待整理任务，自动统计已规划、待整理和已完成任务数量，计算项目进度</div>
            </div>
            
            <!-- 功能模块4: 主子任务关系 -->
            <div class="hex-card">
                <div class="hex-icon">🎯</div>
                <div class="hex-title">主子任务关系</div>
                <div class="hex-desc">强大的任务层级管理功能，通过@任务名语法轻松关联主子任务，构建清晰的任务结构树</div>
            </div>
            
            <!-- 功能模块5: 智能筛选 -->
            <div class="hex-card">
                <div class="hex-icon">🔍</div>
                <div class="hex-title">智能筛选</div>
                <div class="hex-desc">强大的多维度任务过滤系统，支持按优先级、标签、项目和时间范围等条件组合筛选，快速定位任务</div>
            </div>
            
            <!-- 功能模块6: 数据可视化 -->
            <div class="hex-card">
                <div class="hex-icon">📈</div>
                <div class="hex-title">数据可视化</div>
                <div class="hex-desc">实时生成任务完成率、番茄钟达成率等关键指标的可视化图表，帮助您分析工作模式和效率</div>
            </div>
        </div>
        
        <!-- 工作流程 -->
        <div class="workflow">
            <div class="workflow-step">
                <div class="step-title">快速捕获</div>
            </div>
            <div class="workflow-arrow">→</div>
            <div class="workflow-step">
                <div class="step-title">任务整理</div>
            </div>
            <div class="workflow-arrow">→</div>
            <div class="workflow-step">
                <div class="step-title">专注执行</div>
            </div>
            <div class="workflow-arrow">→</div>
            <div class="workflow-step">
                <div class="step-title">数据分析</div>
            </div>
            <div class="workflow-arrow">→</div>
            <div class="workflow-step">
                <div class="step-title">持续改进</div>
            </div>
        </div>
        
        <!-- 底部信息 -->
        <div class="footer">
            Obsidian 任务管理系统 | 版本 v1.0 | 基于 Dataview 插件构建
        </div>
    </div>
</body>
</html> 