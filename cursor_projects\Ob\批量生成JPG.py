#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
番茄钟推广图片批量JPG生成器
使用简单的HTML转图片方法
"""

import os
import subprocess
import time

def html_to_jpg_simple(html_file, jpg_file):
    """简单的HTML转JPG方法"""
    try:
        # 使用Chrome headless模式截图
        chrome_cmd = [
            'chrome',
            '--headless',
            '--disable-gpu',
            '--window-size=1200,1800',
            '--screenshot=' + jpg_file,
            'file:///' + os.path.abspath(html_file).replace('\\', '/')
        ]
        
        result = subprocess.run(chrome_cmd, capture_output=True, text=True, timeout=30)
        
        if os.path.exists(jpg_file):
            print(f"✅ 成功生成: {jpg_file}")
            return True
        else:
            print(f"❌ 生成失败: {jpg_file}")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def main():
    """主函数"""
    # 定义所有风格
    styles = [
        ("番茄钟推广图.html", "番茄钟推广图_深蓝科技风.jpg"),
        ("番茄钟推广图_橙色活力风.html", "番茄钟推广图_橙色活力风.jpg"),
        ("番茄钟推广图_白色商务风.html", "番茄钟推广图_白色商务风.jpg"),
        ("番茄钟推广图_紫色梦幻风.html", "番茄钟推广图_紫色梦幻风.jpg"),
        ("番茄钟推广图_绿色清新风.html", "番茄钟推广图_绿色清新风.jpg"),
    ]
    
    print("🎨 开始批量生成番茄钟推广图片...")
    success_count = 0
    
    # 先列出所有HTML文件
    print("\n📋 检查HTML文件:")
    for html_file, jpg_file in styles:
        if os.path.exists(html_file):
            print(f"✅ {html_file}")
        else:
            print(f"❌ {html_file} (不存在)")
    
    print(f"\n📁 当前目录: {os.getcwd()}")
    print("📝 HTML文件列表:")
    html_files = [f for f in os.listdir('.') if f.endswith('.html') and '番茄钟' in f]
    for f in html_files:
        print(f"  - {f}")
    
    print("\n🎯 建议：请手动在浏览器中打开HTML文件，然后截图保存为JPG格式")
    print("或者使用在线HTML转图片工具")

if __name__ == "__main__":
    main()
