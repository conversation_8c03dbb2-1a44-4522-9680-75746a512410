# 🛠️ HTML模板开发文档

> 详细介绍如何开发和定制推广图HTML模板

## 📋 目录

- [🎯 模板结构](#-模板结构)
- [🎨 样式系统](#-样式系统)
- [🔧 变量系统](#-变量系统)
- [📱 响应式设计](#-响应式设计)
- [🧪 测试调试](#-测试调试)
- [📦 模板打包](#-模板打包)

## 🎯 模板结构

### 基础模板结构

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <style>
        /* 样式定义 */
    </style>
</head>
<body>
    <div class="container">
        <!-- 内容区域 -->
    </div>
</body>
</html>
```

### 模板变量

```html
<!-- 文本变量 -->
<h1>{{title}}</h1>
<p>{{description}}</p>

<!-- 列表变量 -->
{{#features}}
<li>{{.}}</li>
{{/features}}

<!-- 条件变量 -->
{{#show_logo}}
<img src="{{logo_url}}" alt="Logo">
{{/show_logo}}

<!-- 样式变量 -->
<div style="background-color: {{background_color}};">
```

## 🎨 样式系统

### CSS变量定义

```css
:root {
    /* 颜色系统 */
    --primary-color: {{primary_color}};
    --secondary-color: {{secondary_color}};
    --text-color: {{text_color}};
    --background-color: {{background_color}};
    
    /* 字体系统 */
    --font-family: {{font_family}};
    --font-size-large: {{font_size_large}};
    --font-size-medium: {{font_size_medium}};
    --font-size-small: {{font_size_small}};
    
    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* 圆角系统 */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    
    /* 阴影系统 */
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
}
```

### 布局系统

```css
/* Flexbox布局 */
.flex-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

/* Grid布局 */
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
}

/* 卡片布局 */
.card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-lg);
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}
```

## 🔧 变量系统

### 变量配置文件

```json
{
  "template_name": "bento_grid",
  "variables": {
    "colors": {
      "primary": "#007bff",
      "secondary": "#6c757d",
      "success": "#28a745",
      "warning": "#ffc107",
      "danger": "#dc3545",
      "background": "#f8f9fa",
      "text": "#212529"
    },
    "typography": {
      "font_family": "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
      "font_size_h1": "2.5rem",
      "font_size_h2": "2rem",
      "font_size_h3": "1.5rem",
      "font_size_body": "1rem",
      "font_weight_bold": "700",
      "font_weight_medium": "500",
      "line_height": "1.6"
    },
    "spacing": {
      "container_padding": "2rem",
      "section_margin": "3rem",
      "element_margin": "1rem"
    },
    "dimensions": {
      "container_width": "1200px",
      "container_height": "800px",
      "border_radius": "12px"
    }
  }
}
```

### 变量处理器

```python
class TemplateProcessor:
    def __init__(self, template_path, variables):
        self.template_path = template_path
        self.variables = variables
        
    def process_variables(self, content):
        """处理模板变量"""
        for key, value in self.variables.items():
            if isinstance(value, dict):
                # 处理嵌套变量
                for sub_key, sub_value in value.items():
                    placeholder = f"{{{{{key}_{sub_key}}}}}"
                    content = content.replace(placeholder, str(sub_value))
            else:
                # 处理简单变量
                placeholder = f"{{{{{key}}}}}"
                content = content.replace(placeholder, str(value))
        
        return content
        
    def render(self, data):
        """渲染模板"""
        with open(self.template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
            
        # 合并变量和数据
        all_variables = {**self.variables, **data}
        
        # 处理变量替换
        rendered_content = self.process_variables(template_content)
        
        return rendered_content
```

## 📱 响应式设计

### 媒体查询

```css
/* 移动设备 */
@media (max-width: 768px) {
    .container {
        padding: var(--spacing-md);
    }
    
    .grid-container {
        grid-template-columns: 1fr;
    }
    
    .title {
        font-size: 1.5rem;
    }
}

/* 平板设备 */
@media (min-width: 769px) and (max-width: 1024px) {
    .grid-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 桌面设备 */
@media (min-width: 1025px) {
    .grid-container {
        grid-template-columns: repeat(3, 1fr);
    }
}
```

### 动态尺寸

```css
/* 使用视口单位 */
.hero-section {
    height: 100vh;
    width: 100vw;
}

/* 使用容器查询 */
@container (min-width: 400px) {
    .card {
        flex-direction: row;
    }
}

/* 使用clamp()函数 */
.title {
    font-size: clamp(1.5rem, 4vw, 3rem);
}
```

## 🧪 测试调试

### 模板测试

```python
import unittest
from template_processor import TemplateProcessor

class TestTemplateProcessor(unittest.TestCase):
    def setUp(self):
        self.variables = {
            "colors": {"primary": "#007bff"},
            "typography": {"font_family": "Arial"}
        }
        self.processor = TemplateProcessor("test_template.html", self.variables)
        
    def test_variable_replacement(self):
        """测试变量替换"""
        content = "<div style='color: {{colors_primary}};'>Test</div>"
        result = self.processor.process_variables(content)
        
        self.assertIn("#007bff", result)
        self.assertNotIn("{{colors_primary}}", result)
        
    def test_render_with_data(self):
        """测试数据渲染"""
        data = {"title": "测试标题", "description": "测试描述"}
        result = self.processor.render(data)
        
        self.assertIn("测试标题", result)
        self.assertIn("测试描述", result)
```

### 浏览器测试

```python
from playwright.sync_api import sync_playwright

def test_template_rendering():
    """测试模板在浏览器中的渲染效果"""
    with sync_playwright() as p:
        browser = p.chromium.launch()
        page = browser.new_page()
        
        # 加载模板HTML
        page.set_content(rendered_html)
        
        # 等待渲染完成
        page.wait_for_load_state("networkidle")
        
        # 截图验证
        screenshot = page.screenshot()
        
        # 验证元素存在
        title_element = page.query_selector(".title")
        assert title_element is not None
        
        browser.close()
```

### 性能测试

```python
import time
from memory_profiler import profile

@profile
def performance_test():
    """测试模板处理性能"""
    start_time = time.time()
    
    # 批量处理模板
    for i in range(1000):
        processor.render(test_data)
    
    end_time = time.time()
    print(f"处理1000个模板耗时: {end_time - start_time:.2f}秒")
```

## 📦 模板打包

### 模板结构

```
templates/
├── bento_grid/
│   ├── template.html
│   ├── styles.css
│   ├── config.json
│   ├── preview.png
│   └── README.md
├── modern_card/
│   ├── template.html
│   ├── styles.css
│   ├── config.json
│   ├── preview.png
│   └── README.md
└── template_registry.json
```

### 模板注册

```json
{
  "templates": [
    {
      "id": "bento_grid",
      "name": "Bento Grid布局",
      "description": "现代化网格卡片布局",
      "category": "modern",
      "tags": ["grid", "cards", "modern"],
      "author": "测试库团队",
      "version": "1.0.0",
      "preview": "bento_grid/preview.png",
      "config": "bento_grid/config.json",
      "template": "bento_grid/template.html"
    }
  ]
}
```

### 模板验证

```python
def validate_template(template_path):
    """验证模板完整性"""
    required_files = ["template.html", "config.json", "README.md"]
    
    for file_name in required_files:
        file_path = template_path / file_name
        if not file_path.exists():
            raise ValueError(f"缺少必需文件: {file_name}")
    
    # 验证HTML语法
    with open(template_path / "template.html", 'r') as f:
        html_content = f.read()
        # 使用HTML解析器验证语法
        
    # 验证配置文件
    with open(template_path / "config.json", 'r') as f:
        config = json.load(f)
        required_keys = ["template_name", "variables"]
        for key in required_keys:
            if key not in config:
                raise ValueError(f"配置文件缺少必需键: {key}")
    
    return True
```

---

## 📚 相关资源

- [推广图制作指南](promotional-image-guide.md)
- [CSS样式指南](css-style-guide.md)
- [模板示例库](template-examples.md)
- [最佳实践指南](best-practices.md)

---

*让模板开发变得简单高效 🛠️*
