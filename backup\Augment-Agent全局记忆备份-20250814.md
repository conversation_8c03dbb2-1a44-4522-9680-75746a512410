# Augment Agent 全局记忆备份

> 📅 备份时间：2025-08-14 星期四
> 🤖 备份方式：自动化脚本
> 📝 备份类型：manual

## ⚠️ 重要提示

此文件由自动化脚本生成，需要手动更新实际的记忆内容。

### 手动操作步骤：
1. 在 Augment Agent 中查看当前全局记忆
2. 将记忆内容复制到此文件
3. 或者使用 "导出对话：记忆备份" 命令

### 核心记忆内容检查清单：
- [ ] 中文交流偏好
- [ ] 文档生成规则（生成总结性Markdown，不生成测试脚本）
- [ ] 编译运行协助偏好
- [ ] 翻译规则模板
- [ ] 对话导出约定
- [ ] MCP工具配置经验
- [ ] PowerShell使用偏好
- [ ] 知识重构原则
- [ ] 任务管理偏好

## 📋 自动备份记录

- 备份时间：2025-08-14 14:25:42
- 备份类型：manual
- 脚本版本：v1.0
- 项目路径：C:\Users\<USER>\Desktop\测试库

---

**下次备份提醒**：请在 Augment Agent 中执行记忆导出，更新此文件内容。
