<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obsidian项目管理系统推广图片</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background: #121212;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .promo-container {
            width: 1200px;
            height: 1800px;
            position: relative;
            overflow: hidden;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #1a1a35 0%, #2b1e4d 50%, #1e2c4d 100%);
            padding: 40px;
        }
        
        .glow-effect {
            position: absolute;
            width: 500px;
            height: 500px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(138, 43, 226, 0.2) 0%, rgba(0, 128, 255, 0.1) 50%, rgba(0, 0, 0, 0) 70%);
            filter: blur(60px);
            z-index: 0;
        }
        
        .glow-1 {
            top: -100px;
            left: -100px;
        }
        
        .glow-2 {
            bottom: -100px;
            right: -100px;
            background: radial-gradient(circle, rgba(72, 61, 139, 0.2) 0%, rgba(25, 118, 210, 0.1) 50%, rgba(0, 0, 0, 0) 70%);
        }
        
        .glow-3 {
            top: 50%;
            left: 70%;
            background: radial-gradient(circle, rgba(123, 104, 238, 0.15) 0%, rgba(72, 61, 139, 0.05) 50%, rgba(0, 0, 0, 0) 70%);
        }
        
        .header {
            text-align: center;
            padding: 60px 0 40px;
            position: relative;
            z-index: 1;
        }
        
        .logo-container {
            margin-bottom: 30px;
        }
        
        .logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #6e48aa, #9168e8);
            border-radius: 30px;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            box-shadow: 0 10px 20px rgba(110, 72, 170, 0.3);
        }
        
        .logo-icon {
            font-size: 60px;
        }
        
        .main-title {
            font-size: 56px;
            font-weight: bold;
            background: linear-gradient(to right, #6e48aa, #48a0f2);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
            letter-spacing: 1px;
        }
        
        .subtitle {
            font-size: 28px;
            color: #a1b5d8;
            margin-bottom: 30px;
            letter-spacing: 1px;
        }
        
        .description {
            font-size: 18px;
            color: #7a8ca8;
            max-width: 800px;
            margin: 0 auto 60px;
            line-height: 1.7;
        }
        
        .features-container {
            position: relative;
            z-index: 2;
            margin: 0 auto;
            max-width: 1000px;
        }
        
        .feature {
            background: rgba(41, 41, 61, 0.7);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(138, 43, 226, 0.2);
            padding: 40px;
            margin-bottom: 40px;
            display: flex;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }
        
        .feature::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, #6e48aa, #48a0f2);
        }
        
        .feature:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(138, 43, 226, 0.4);
        }
        
        .feature-icon-container {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, rgba(110, 72, 170, 0.2), rgba(72, 160, 242, 0.2));
            border-radius: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 30px;
            flex-shrink: 0;
        }
        
        .feature-icon {
            font-size: 48px;
            background: linear-gradient(to bottom right, #6e48aa, #48a0f2);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .feature-content {
            flex-grow: 1;
        }
        
        .feature-title {
            font-size: 30px;
            font-weight: bold;
            margin-bottom: 15px;
            color: white;
        }
        
        .feature-desc {
            font-size: 18px;
            color: #a1b5d8;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            font-size: 16px;
            color: #7a8ca8;
        }
        
        .feature-item::before {
            content: "✓";
            display: inline-block;
            margin-right: 10px;
            color: #48a0f2;
            font-weight: bold;
        }
        
        .dashboard-preview {
            width: 100%;
            margin: 60px auto;
            background: rgba(30, 30, 48, 0.7);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 2;
        }
        
        .dashboard-header {
            background: rgba(41, 41, 61, 0.7);
            padding: 15px 20px;
            display: flex;
            align-items: center;
        }
        
        .window-controls {
            display: flex;
            margin-right: 20px;
        }
        
        .window-control {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .control-red {
            background: #ff5f57;
        }
        
        .control-yellow {
            background: #ffbd2e;
        }
        
        .control-green {
            background: #28c841;
        }
        
        .dashboard-title {
            color: #a1b5d8;
            font-size: 14px;
        }
        
        .dashboard-content {
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-gap: 20px;
            min-height: 300px;
        }
        
        .dashboard-card {
            background: rgba(46, 46, 66, 0.7);
            border-radius: 10px;
            padding: 15px;
            height: 120px;
        }
        
        .chart {
            width: 100%;
            height: 60px;
            background: linear-gradient(to right, rgba(110, 72, 170, 0.3), rgba(72, 160, 242, 0.3));
            border-radius: 8px;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }
        
        .chart::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25'%3E%3Cpath d='M0,50 Q25,20 50,50 T100,50' stroke='rgba(138, 43, 226, 0.6)' fill='none' stroke-width='2'/%3E%3C/svg%3E");
            background-size: 100px 100%;
            background-position: center;
            opacity: 0.7;
        }
        
        .bar-graph {
            width: 100%;
            height: 60px;
            display: flex;
            align-items: flex-end;
            justify-content: space-between;
            padding-top: 10px;
        }
        
        .bar {
            width: 15%;
            background: linear-gradient(to top, #6e48aa, #48a0f2);
            border-radius: 3px 3px 0 0;
        }
        
        .bar-1 { height: 60%; }
        .bar-2 { height: 80%; }
        .bar-3 { height: 40%; }
        .bar-4 { height: 70%; }
        .bar-5 { height: 50%; }
        .bar-6 { height: 65%; }
        
        .card-title {
            font-size: 16px;
            color: white;
            margin-bottom: 10px;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin: 60px auto;
            max-width: 1000px;
        }
        
        .metric-card {
            background: rgba(41, 41, 61, 0.7);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(138, 43, 226, 0.15);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            border-color: rgba(138, 43, 226, 0.4);
            transform: translateY(-5px);
        }
        
        .metric-value {
            font-size: 48px;
            font-weight: bold;
            background: linear-gradient(to right, #6e48aa, #48a0f2);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 15px;
        }
        
        .metric-label {
            font-size: 18px;
            color: #a1b5d8;
        }
        
        .cta-container {
            text-align: center;
            margin: 80px auto 40px;
            position: relative;
            z-index: 2;
        }
        
        .cta-button {
            background: linear-gradient(135deg, #6e48aa, #48a0f2);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 20px 50px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(110, 72, 170, 0.4);
        }
        
        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(110, 72, 170, 0.5);
        }
        
        .footer {
            text-align: center;
            margin-top: 80px;
            color: #7a8ca8;
            font-size: 14px;
            position: relative;
            z-index: 2;
        }
    </style>
</head>
<body>
    <div class="promo-container">
        <!-- 发光效果 -->
        <div class="glow-effect glow-1"></div>
        <div class="glow-effect glow-2"></div>
        <div class="glow-effect glow-3"></div>
        
        <!-- 头部 -->
        <div class="header">
            <div class="logo-container">
                <div class="logo">
                    <div class="logo-icon">📊</div>
                </div>
            </div>
            <h1 class="main-title">Obsidian项目管理系统</h1>
            <h2 class="subtitle">数据驱动 · 高效协作 · 智能决策</h2>
            <p class="description">
                基于Obsidian构建的专业项目管理系统，通过智能数据分析和可视化技术，
                为团队提供全方位项目监控和决策支持，助力提升项目执行效率和成功率。
            </p>
        </div>
        
        <!-- 功能特性 -->
        <div class="features-container">
            <!-- 功能1：实时数据处理 -->
            <div class="feature">
                <div class="feature-icon-container">
                    <div class="feature-icon">📡</div>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">实时数据处理</h3>
                    <p class="feature-desc">
                        强大的数据采集与处理引擎，实时监控项目关键指标，智能识别风险点并提供预警。
                    </p>
                    <div class="feature-list">
                        <div class="feature-item">多源数据整合</div>
                        <div class="feature-item">智能异常检测</div>
                        <div class="feature-item">自动化数据同步</div>
                        <div class="feature-item">实时预警机制</div>
                    </div>
                </div>
            </div>
            
            <!-- 功能2：多维度可视化 -->
            <div class="feature">
                <div class="feature-icon-container">
                    <div class="feature-icon">📈</div>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">多维度可视化</h3>
                    <p class="feature-desc">
                        丰富的数据可视化组件库，支持多种维度的项目数据展示，让复杂数据一目了然。
                    </p>
                    <div class="feature-list">
                        <div class="feature-item">项目进度甘特图</div>
                        <div class="feature-item">资源分配热力图</div>
                        <div class="feature-item">绩效指标仪表盘</div>
                        <div class="feature-item">趋势预测图表</div>
                    </div>
                </div>
            </div>
            
            <!-- 功能3：自动报告生成 -->
            <div class="feature">
                <div class="feature-icon-container">
                    <div class="feature-icon">📑</div>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">自动报告生成</h3>
                    <p class="feature-desc">
                        智能报告引擎可自动生成各类项目报告，支持自定义模板，一键生成专业文档。
                    </p>
                    <div class="feature-list">
                        <div class="feature-item">项目周期报告</div>
                        <div class="feature-item">绩效评估报告</div>
                        <div class="feature-item">风险分析报告</div>
                        <div class="feature-item">资源使用报告</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 仪表盘预览 -->
        <div class="dashboard-preview">
            <div class="dashboard-header">
                <div class="window-controls">
                    <div class="window-control control-red"></div>
                    <div class="window-control control-yellow"></div>
                    <div class="window-control control-green"></div>
                </div>
                <div class="dashboard-title">项目仪表盘</div>
            </div>
            <div class="dashboard-content">
                <div class="dashboard-card">
                    <div class="card-title">项目进度</div>
                    <div class="chart"></div>
                </div>
                <div class="dashboard-card">
                    <div class="card-title">资源分配</div>
                    <div class="bar-graph">
                        <div class="bar bar-1"></div>
                        <div class="bar bar-2"></div>
                        <div class="bar bar-3"></div>
                        <div class="bar bar-4"></div>
                        <div class="bar bar-5"></div>
                        <div class="bar bar-6"></div>
                    </div>
                </div>
                <div class="dashboard-card">
                    <div class="card-title">风险监控</div>
                    <div class="chart"></div>
                </div>
            </div>
        </div>
        
        <!-- 性能指标 -->
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value">98%</div>
                <div class="metric-label">项目成功率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">65%</div>
                <div class="metric-label">效率提升</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">30%</div>
                <div class="metric-label">成本节约</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">24/7</div>
                <div class="metric-label">实时监控</div>
            </div>
        </div>
        
        <!-- 号召性用语 -->
        <div class="cta-container">
            <button class="cta-button">立即体验</button>
        </div>
        
        <!-- 页脚 -->
        <div class="footer">
            Obsidian项目管理系统 | 版本 2.0 | 专业版
        </div>
    </div>
</body>
</html> 