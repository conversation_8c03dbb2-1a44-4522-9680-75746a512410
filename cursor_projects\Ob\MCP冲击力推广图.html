<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP冲击力推广图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #000000 100%);
            color: white;
            width: 1200px;
            height: 1800px;
            overflow: hidden;
            position: relative;
        }
        
        .tech-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0,255,255,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            opacity: 0.3;
        }
        
        .container {
            padding: 40px;
            height: 100%;
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 2;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }
        
        .main-title {
            font-size: 56px;
            font-weight: 900;
            background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
            text-shadow: 0 0 30px rgba(0,255,255,0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { filter: drop-shadow(0 0 20px rgba(0,255,255,0.5)); }
            to { filter: drop-shadow(0 0 30px rgba(255,0,255,0.8)); }
        }
        
        .subtitle {
            font-size: 24px;
            color: #00ffff;
            margin-bottom: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .tagline {
            font-size: 16px;
            color: rgba(255,255,255,0.8);
            font-weight: 400;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            flex: 1;
        }
        
        .services-section {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }
        
        .service-card {
            background: linear-gradient(135deg, rgba(0,255,255,0.1), rgba(255,0,255,0.1));
            border: 2px solid rgba(0,255,255,0.3);
            border-radius: 12px;
            padding: 20px 15px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .service-card:hover::before {
            left: 100%;
        }
        
        .service-card:hover {
            transform: scale(1.05);
            border-color: rgba(0,255,255,0.8);
            box-shadow: 0 0 20px rgba(0,255,255,0.4);
        }
        
        .service-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .service-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .service-title {
            font-size: 14px;
            font-weight: 700;
            color: #00ffff;
            text-transform: uppercase;
        }
        
        .service-name {
            font-size: 11px;
            color: #ff00ff;
            font-family: 'Courier New', monospace;
            background: rgba(0,0,0,0.5);
            padding: 2px 6px;
            border-radius: 4px;
            margin-bottom: 8px;
            border: 1px solid rgba(255,0,255,0.3);
        }
        
        .service-desc {
            font-size: 11px;
            line-height: 1.3;
            color: rgba(255,255,255,0.9);
        }
        
        .stats-panel {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .performance-stats {
            background: linear-gradient(135deg, rgba(255,0,0,0.2), rgba(255,255,0,0.2));
            border: 2px solid rgba(255,255,0,0.5);
            border-radius: 12px;
            padding: 20px;
        }
        
        .stats-title {
            font-size: 16px;
            font-weight: 700;
            color: #ffff00;
            margin-bottom: 15px;
            text-align: center;
            text-transform: uppercase;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 12px;
            background: rgba(0,0,0,0.3);
            border-radius: 6px;
            border-left: 3px solid #ffff00;
        }
        
        .stat-label {
            font-size: 12px;
            color: rgba(255,255,255,0.8);
        }
        
        .stat-value {
            font-size: 14px;
            font-weight: 700;
            color: #ffff00;
        }
        
        .tech-specs {
            background: linear-gradient(135deg, rgba(0,255,0,0.2), rgba(0,0,255,0.2));
            border: 2px solid rgba(0,255,0,0.5);
            border-radius: 12px;
            padding: 20px;
        }
        
        .config-info {
            background: linear-gradient(135deg, rgba(255,0,255,0.2), rgba(255,255,255,0.1));
            border: 2px solid rgba(255,0,255,0.5);
            border-radius: 12px;
            padding: 20px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 11px;
        }
        
        .info-icon {
            font-size: 14px;
            margin-right: 8px;
            color: #00ff00;
        }
        
        .info-text {
            color: rgba(255,255,255,0.9);
        }
        
        .bottom-banner {
            background: linear-gradient(90deg, rgba(255,0,0,0.3), rgba(255,255,0,0.3), rgba(0,255,0,0.3), rgba(0,255,255,0.3), rgba(0,0,255,0.3), rgba(255,0,255,0.3));
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            margin-top: 20px;
            animation: rainbow 3s linear infinite;
        }
        
        @keyframes rainbow {
            0% { background-position: 0% 50%; }
            100% { background-position: 100% 50%; }
        }
        
        .banner-text {
            font-size: 14px;
            font-weight: 700;
            color: #ffffff;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .version-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,0,0,0.8);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            border: 2px solid #ff0000;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="tech-grid"></div>
    <div class="version-badge">v2025.6</div>
    
    <div class="container">
        <div class="header">
            <h1 class="main-title">MCP PROTOCOL SUITE</h1>
            <p class="subtitle">9 CORE SERVICES • ENTERPRISE READY</p>
            <p class="tagline">Model Context Protocol 企业级部署解决方案</p>
        </div>
        
        <div class="main-content">
            <div class="services-section">
                <div class="service-card">
                    <div class="service-header">
                        <span class="service-icon">💬</span>
                        <span class="service-title">FEEDBACK</span>
                    </div>
                    <div class="service-name">mcp-feedback-enhanced</div>
                    <p class="service-desc">实时系统监控 • 性能诊断 • 环境检测 • 用户交互反馈收集</p>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <span class="service-icon">📚</span>
                        <span class="service-title">KNOWLEDGE</span>
                    </div>
                    <div class="service-name">mcp-obsidian</div>
                    <p class="service-desc">知识库集成 • 笔记管理 • 全文搜索 • 内容创建与编辑</p>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <span class="service-icon">📖</span>
                        <span class="service-title">DOCS</span>
                    </div>
                    <div class="service-name">context7</div>
                    <p class="service-desc">API文档查询 • 代码示例 • 技术资料 • 实时更新</p>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <span class="service-icon">🧠</span>
                        <span class="service-title">THINKING</span>
                    </div>
                    <div class="service-name">sequential-thinking</div>
                    <p class="service-desc">结构化分析 • 逻辑推理 • 问题分解 • 决策支持</p>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <span class="service-icon">🌐</span>
                        <span class="service-title">AUTOMATION</span>
                    </div>
                    <div class="service-name">playwright</div>
                    <p class="service-desc">浏览器自动化 • 网页截图 • 表单操作 • 数据抓取</p>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <span class="service-icon">🎨</span>
                        <span class="service-title">AI IMAGE</span>
                    </div>
                    <div class="service-name">replicate-flux</div>
                    <p class="service-desc">高质量图像生成 • 多风格支持 • 批量处理 • API集成</p>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <span class="service-icon">🖼️</span>
                        <span class="service-title">CREATIVE</span>
                    </div>
                    <div class="service-name">together-image-gen</div>
                    <p class="service-desc">快速创作 • 实时生成 • 风格迁移 • 图像编辑</p>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <span class="service-icon">📋</span>
                        <span class="service-title">TASK MGR</span>
                    </div>
                    <div class="service-name">shrimp-task-manager</div>
                    <p class="service-desc">智能规划 • 任务分解 • 进度跟踪 • 工作流优化</p>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <span class="service-icon">🌍</span>
                        <span class="service-title">FETCH</span>
                    </div>
                    <div class="service-name">fetch</div>
                    <p class="service-desc">网络请求 • 数据获取 • API调用 • 内容解析</p>
                </div>
            </div>
            
            <div class="stats-panel">
                <div class="performance-stats">
                    <div class="stats-title">PERFORMANCE METRICS</div>
                    <div class="stat-item">
                        <span class="stat-label">配置成功率</span>
                        <span class="stat-value">98.7%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">平均响应时间</span>
                        <span class="stat-value">&lt;200ms</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">并发连接数</span>
                        <span class="stat-value">1000+</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">正常运行时间</span>
                        <span class="stat-value">99.9%</span>
                    </div>
                </div>
                
                <div class="tech-specs">
                    <div class="stats-title">TECH STACK</div>
                    <div class="info-item">
                        <span class="info-icon">⚡</span>
                        <span class="info-text">Node.js 18+ Runtime</span>
                    </div>
                    <div class="info-item">
                        <span class="info-icon">🐍</span>
                        <span class="info-text">Python 3.8+ Support</span>
                    </div>
                    <div class="info-item">
                        <span class="info-icon">🔧</span>
                        <span class="info-text">UV Package Manager</span>
                    </div>
                    <div class="info-item">
                        <span class="info-icon">🌐</span>
                        <span class="info-text">REST API Interface</span>
                    </div>
                    <div class="info-item">
                        <span class="info-icon">🔒</span>
                        <span class="info-text">Enterprise Security</span>
                    </div>
                </div>
                
                <div class="config-info">
                    <div class="stats-title">DEPLOYMENT</div>
                    <div class="info-item">
                        <span class="info-icon">💻</span>
                        <span class="info-text">Cursor IDE Ready</span>
                    </div>
                    <div class="info-item">
                        <span class="info-icon">🚀</span>
                        <span class="info-text">Augment Compatible</span>
                    </div>
                    <div class="info-item">
                        <span class="info-icon">⚙️</span>
                        <span class="info-text">One-Click Setup</span>
                    </div>
                    <div class="info-item">
                        <span class="info-icon">📊</span>
                        <span class="info-text">Real-time Monitoring</span>
                    </div>
                    <div class="info-item">
                        <span class="info-icon">🔄</span>
                        <span class="info-text">Auto-Update System</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bottom-banner">
            <div class="banner-text">🚀 ENTERPRISE READY • PRODUCTION TESTED • 24/7 SUPPORT 🚀</div>
        </div>
    </div>
</body>
</html>
