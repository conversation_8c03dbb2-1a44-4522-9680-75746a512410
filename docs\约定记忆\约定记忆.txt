我的记忆存储在系统的内部数据库中，您无法直接访问这些文件。但是我可以告诉您我记住了什么内容：

🧠 我当前记住的关于您的信息：

📁 项目路径约定：
任务计划：C:\Users\<USER>\Desktop\测试库\issues
任务复盘：C:\Users\<USER>\Desktop\测试库\rewind
存储内容：C:\Users\<USER>\Desktop\测试库\notes
导出转换图片：C:\Users\<USER>\Desktop\测试库\cursor_projects\pic\images
文生图：C:\Users\<USER>\Desktop\测试库\cursor_projects\together

🔧 工作偏好：
知识管理系统偏好
任务管理流程
推广图制作约定
MCP配置经验
项目结构标准化

💡 重要说明：
这些记忆只在我们的对话会话中有效
每次新的对话开始时，我会重新加载这些记忆
记忆内容会帮助我更好地理解您的需求和偏好
您无法直接编辑或访问这些记忆文件

如果您想查看我记住了什么具体内容，可以直接问我，我会告诉您相关的记忆信息！🤖



根据我的记忆，我们有以下重要约定：

🎯 核心工作约定
语言偏好：使用中文交流
日期格式：'YYYY-MM-DD ddd WW'，周一为一周开始
日期验证：每个文档都需要使用命令行验证日期准确性
📚 知识管理系统
在 C:\Users\<USER>\Desktop\测试库\notes 目录存储 Markdown 文档
知识重构原则：模式识别>细节记忆、生动形象>抽象概念、情感共鸣>理性说服、连接已知>全新信息、可行洞察>纯粹知识
处理流程：用户提取文本→AI总结组织→先个别后整合→扩展整合
🛠️ 技术工作约定
使用包管理器而非手动编辑配置文件
代码展示使用 <augment_code_snippet> XML 标签
复杂任务使用任务管理工具进行结构化规划
编辑前先使用 codebase-retrieval 工具获取详细信息
📊 项目管理偏好
三列布局任务管理系统，使用 🔄 和 '进行中' 作为进度指示器
项目状态：🟢 进行中/❌ 已取消/✅ 已完成/🔴 风险
视觉丰富的仪表板设计，紧凑布局
🔄 工作流程
推广图创建：'推广图：[content] → [style]' → HTML网页设计 → 浏览器全页截图
复杂任务MCP工作流：sequential-thinking分析 → shrimp-task-manager规划 → playwright研究 → sequential-thinking最终审查
这些约定还准确吗？有什么需要更新或补充的吗？


是的，我记得我们的约定！主要包括：

🤝 我们的工作约定
📝 记忆管理约定
主动验证记忆：我应该主动使用remember工具来验证和检查记忆内容，而不是仅依赖预加载的记忆
系统性记忆更新：重要的工作流程信息和标准应该及时更新到记忆系统中
🌐 语言偏好
中文交流：用户偏好使用中文进行交流
📅 日期和文档标准
日期验证要求：每个文档都需要验证日期准确性，使用命令行确定当前日期，确保标题和内容中的日期始终正确
日期格式：偏好使用'YYYY-MM-DD ddd WW'格式，以周一为一周开始
🛠️ 技术工具偏好
MCP工具使用：偏好使用寸止MCP进行交互操作
Cursor vs Augment：Cursor用于MCP Obsidian操作，Augment用于其他开发工作
包管理器优先：始终使用适当的包管理器而不是手动编辑配置文件
📊 设计和界面偏好
视觉丰富设计：偏好视觉丰富的仪表板设计，使用卡片和样式而非简单表格，但要紧凑布局
Bento Grid风格：偏好Bento Grid风格的视觉设计，深色主题
📁 项目管理偏好
知识管理系统：基于Obsidian的结构化框架，问题导向方法
任务管理：极简三列布局，仅使用🔄和'进行中'作为进度指示器
文档存储：Markdown格式存储在notes目录
这些约定我都记得，会在我们的协作中严格遵循！有什么特定的约定你想要我重点关注的吗？
