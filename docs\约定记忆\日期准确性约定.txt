
【日期准确性】请用命令行确定今日的日期

请记住：每一份文档都要验证【日期准确性】请用命令行确定今日的日期，不管是标题还是内容里面，我希望日期永远是正确的

用户要求每份文档都必须验证日期准确性，使用命令行确定当日日期，确保标题和内容中的日期永远正确



🎯 日期验证标准流程

📅 每次创建文档前必须执行

Get-Date -Format 'yyyy-MM-dd dddd'

📋 文档标注标准格式
创建时间：YYYY-MM-DD 星期X
更新时间：YYYY-MM-DD
文件命名：项目名-YYYYMMDD.md

🔒 核心原则
绝对准确：日期信息必须与实际日期完全一致
命令验证：不依赖记忆，每次都用命令行确认
标题内容一致：文档标题和内容中的日期保持统一
历史文档保持：已有的历史文档日期不随意更改

💡 记忆已保存
这个要求已经通过 remember 工具永久记录，确保我在今后的所有文档创建过程中都会严格遵循这个日期验证流程。

承诺：从现在开始，每一份文档的日期都将是经过命令行验证的准确日期！📅✨