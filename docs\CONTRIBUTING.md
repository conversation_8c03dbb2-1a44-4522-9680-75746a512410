# 🤝 贡献指南

> 欢迎参与测试库项目的开发！本指南将帮助您了解如何为项目做出贡献。

## 📋 目录

- [🎯 贡献方式](#-贡献方式)
- [🚀 快速开始](#-快速开始)
- [📝 提交规范](#-提交规范)
- [🔍 代码审查](#-代码审查)
- [🧪 测试要求](#-测试要求)
- [📚 文档贡献](#-文档贡献)
- [🐛 问题报告](#-问题报告)
- [💡 功能建议](#-功能建议)
- [🏆 贡献者认可](#-贡献者认可)

## 🎯 贡献方式

### 代码贡献
- 🐛 **修复Bug**：修复已知问题和错误
- ✨ **新功能**：添加新的功能模块
- 🔧 **优化改进**：性能优化和代码重构
- 📝 **文档完善**：改进文档和注释

### 非代码贡献
- 🐛 **问题报告**：报告Bug和使用问题
- 💡 **功能建议**：提出新功能想法
- 📖 **文档改进**：完善使用指南和教程
- 🌐 **翻译工作**：多语言支持
- 🎨 **设计优化**：界面和用户体验改进

## 🚀 快速开始

### 1. 准备开发环境

```bash
# 1. Fork项目到您的GitHub账户
# 2. 克隆您的Fork
git clone https://github.com/YOUR_USERNAME/测试库.git
cd 测试库

# 3. 添加上游仓库
git remote add upstream https://github.com/ORIGINAL_OWNER/测试库.git

# 4. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 5. 安装依赖
python scripts/install-dependencies.py

# 6. 验证环境
python scripts/verify-installation.py
```

### 2. 创建功能分支

```bash
# 1. 切换到develop分支
git checkout develop
git pull upstream develop

# 2. 创建功能分支
git checkout -b feature/your-feature-name

# 或者修复分支
git checkout -b bugfix/issue-description
```

### 3. 开发和测试

```bash
# 1. 进行开发工作
# 编辑代码文件...

# 2. 运行测试
python -m pytest tests/

# 3. 代码格式化
black . && isort .

# 4. 代码质量检查
python -m pylint **/*.py
```

### 4. 提交更改

```bash
# 1. 添加更改
git add .

# 2. 提交更改（遵循提交规范）
git commit -m "feat(module): 添加新功能描述"

# 3. 推送到您的Fork
git push origin feature/your-feature-name
```

### 5. 创建Pull Request

1. 访问您的GitHub Fork页面
2. 点击 "New Pull Request"
3. 选择 `develop` 作为目标分支
4. 填写PR模板
5. 提交Pull Request

## 📝 提交规范

### 提交消息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

### 类型说明

| 类型 | 说明 | 示例 |
|:---:|:---:|:---:|
| `feat` | 新功能 | `feat(mcp): 添加新的AI工具集成` |
| `fix` | 修复Bug | `fix(generator): 修复推广图生成失败` |
| `docs` | 文档更新 | `docs(readme): 更新安装指南` |
| `style` | 代码格式 | `style(python): 修复代码格式问题` |
| `refactor` | 代码重构 | `refactor(tools): 重构内容生成器` |
| `test` | 测试相关 | `test(unit): 添加内容生成器测试` |
| `chore` | 构建/工具 | `chore(deps): 更新依赖版本` |
| `perf` | 性能优化 | `perf(image): 优化图片生成性能` |

### 范围说明

- `mcp`: MCP工具相关
- `obsidian`: Obsidian集成相关
- `generator`: 内容生成器
- `tools`: 开发工具
- `docs`: 文档
- `config`: 配置文件
- `tests`: 测试代码

### 提交示例

```bash
# 好的提交示例
git commit -m "feat(mcp): 添加Context7工具集成

- 集成Context7技术文档查询功能
- 添加配置模板和环境变量
- 更新MCP配置文档

Closes #123"

# 不好的提交示例
git commit -m "修复bug"  # 太简单，缺乏具体信息
git commit -m "添加了很多新功能和修复了一些问题"  # 太宽泛
```

## 🔍 代码审查

### 审查清单

#### 代码质量
- [ ] 代码遵循项目编码规范
- [ ] 函数和类有适当的文档字符串
- [ ] 变量和函数命名清晰易懂
- [ ] 没有硬编码的配置值
- [ ] 适当的错误处理和日志记录

#### 功能性
- [ ] 功能按预期工作
- [ ] 边界条件得到处理
- [ ] 没有引入新的Bug
- [ ] 向后兼容性得到保持

#### 测试
- [ ] 新功能有相应的测试
- [ ] 所有测试通过
- [ ] 测试覆盖率满足要求
- [ ] 测试用例覆盖主要场景

#### 文档
- [ ] 更新了相关文档
- [ ] API变更有文档说明
- [ ] 示例代码正确可用

### 审查流程

1. **自动检查**：CI/CD流水线自动运行
2. **同行审查**：至少一名维护者审查
3. **测试验证**：在测试环境验证功能
4. **文档检查**：确保文档同步更新

## 🧪 测试要求

### 测试类型

#### 单元测试
- 测试单个函数或方法
- 使用mock隔离外部依赖
- 覆盖正常和异常情况

```python
def test_generate_content_success():
    """测试内容生成成功场景"""
    generator = ContentGenerator()
    config = {"title": "测试", "description": "描述"}
    
    result = generator.generate_content(config)
    
    assert result is not None
    assert result["title"] == "测试"
```

#### 集成测试
- 测试模块间的交互
- 验证API接口功能
- 测试配置和环境

```python
def test_mcp_obsidian_integration():
    """测试MCP Obsidian集成"""
    client = MCPClient()
    
    files = client.list_vault_files()
    
    assert isinstance(files, list)
    assert len(files) > 0
```

#### 端到端测试
- 测试完整的用户流程
- 验证系统整体功能
- 使用真实环境和数据

### 测试运行

```bash
# 运行所有测试
python -m pytest

# 运行特定模块测试
python -m pytest tests/test_content_generator.py

# 生成覆盖率报告
python -m pytest --cov=tools --cov-report=html

# 运行性能测试
python -m pytest --benchmark-only
```

### 测试覆盖率要求

- **新功能**：测试覆盖率 ≥ 80%
- **核心模块**：测试覆盖率 ≥ 90%
- **关键路径**：测试覆盖率 = 100%

## 📚 文档贡献

### 文档类型

#### 用户文档
- 安装和配置指南
- 使用教程和示例
- 常见问题解答
- 故障排除指南

#### 开发者文档
- API参考文档
- 架构设计文档
- 代码注释和文档字符串
- 贡献指南

### 文档规范

#### Markdown格式
```markdown
# 一级标题
## 二级标题
### 三级标题

**粗体文本**
*斜体文本*
`代码片段`

```python
# 代码块
def example_function():
    pass
```

- 列表项1
- 列表项2

| 表头1 | 表头2 |
|:---:|:---:|
| 内容1 | 内容2 |
```

#### 代码示例
- 提供完整可运行的示例
- 包含必要的导入语句
- 添加注释说明关键步骤
- 展示预期的输出结果

#### 图片和图表
- 使用相对路径引用图片
- 提供图片的alt文本
- 优先使用Mermaid图表
- 保持图片文件大小合理

## 🐛 问题报告

### 报告模板

```markdown
## 问题描述
简要描述遇到的问题

## 复现步骤
1. 第一步操作
2. 第二步操作
3. 看到错误

## 预期行为
描述您期望发生的情况

## 实际行为
描述实际发生的情况

## 环境信息
- 操作系统: [例如 Windows 11]
- Python版本: [例如 3.9.7]
- 项目版本: [例如 v1.0.0]

## 附加信息
- 错误日志
- 截图
- 相关配置文件
```

### 报告指南

1. **搜索现有问题**：避免重复报告
2. **提供详细信息**：包含复现步骤和环境信息
3. **使用清晰标题**：简洁描述问题核心
4. **添加相关标签**：帮助分类和优先级排序

## 💡 功能建议

### 建议模板

```markdown
## 功能描述
简要描述建议的新功能

## 使用场景
描述什么情况下需要这个功能

## 解决方案
描述您认为的实现方式

## 替代方案
描述其他可能的实现方式

## 附加信息
- 相关链接
- 参考实现
- 设计草图
```

### 建议指南

1. **明确需求**：清楚描述功能的价值和用途
2. **考虑影响**：评估对现有功能的影响
3. **提供方案**：给出具体的实现建议
4. **保持开放**：接受不同的实现方式

## 🏆 贡献者认可

### 贡献类型

- 🏆 **核心贡献者**：长期活跃的主要贡献者
- 🌟 **功能贡献者**：添加重要新功能
- 🐛 **Bug修复者**：修复重要问题
- 📝 **文档贡献者**：完善项目文档
- 🎨 **设计贡献者**：改进用户体验
- 🌐 **社区贡献者**：帮助用户和推广项目

### 认可方式

- **README致谢**：在项目README中列出贡献者
- **发布说明**：在版本发布中感谢贡献者
- **贡献者徽章**：GitHub Profile徽章
- **社区推荐**：在社交媒体推荐优秀贡献

### 贡献统计

我们使用以下方式统计和展示贡献：

- GitHub贡献图表
- All Contributors规范
- 贡献者排行榜
- 年度贡献报告

---

## 📞 联系我们

- **GitHub Issues**：报告问题和建议
- **GitHub Discussions**：技术讨论和交流
- **邮件联系**：<EMAIL>

感谢您对测试库项目的贡献！🎉

---

*让知识管理变得简单而美好 🌸*
