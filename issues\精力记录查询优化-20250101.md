# 精力记录查询系统优化计划

## 📋 任务背景
用户希望重新优化精力记录查询功能，主要需求：
1. 能够查看所有日记的Capture note记录
2. 支持关键词检索健康等相关信息
3. 优化现有的查询界面和功能

## 🎯 任务目标
1. **扩展查询范围**：从仅查询精力记录扩展到所有Capture note内容
2. **增强搜索功能**：支持多关键词搜索、模糊匹配、高级筛选
3. **优化显示效果**：改进结果展示格式，增加统计分析
4. **提升用户体验**：简化操作流程，增加快捷功能

## 📝 详细计划

### 阶段1：分析现有系统
- [x] 分析当前精力记录查询.md的功能结构
- [x] 了解Daily Notes中Capture note的记录格式
- [x] 识别现有系统的优缺点

### 阶段2：设计新功能架构
- [ ] 设计全量Capture note查询功能
- [ ] 设计多维度筛选器（时间、领域、类型等）
- [ ] 设计高级搜索功能（多关键词、正则表达式）
- [ ] 设计统计分析功能

### 阶段3：实现核心功能
- [ ] 实现全量Capture note数据获取
- [ ] 实现多关键词搜索引擎
- [ ] 实现领域分类筛选（系统、健康、精神、财富等）
- [ ] 实现时间范围筛选

### 阶段4：优化显示界面
- [ ] 重新设计查询界面布局
- [ ] 优化搜索结果展示格式
- [ ] 添加统计图表和数据分析
- [ ] 增加导出和分享功能

### 阶段5：测试和完善
- [ ] 测试各项功能的稳定性
- [ ] 优化查询性能
- [ ] 完善用户交互体验
- [ ] 编写使用说明文档

## 🔍 现有系统分析

### 优点
1. 已有基础的精力记录查询功能
2. 支持按类型筛选和关键词搜索
3. 结果展示格式较为清晰
4. 支持点击跳转到原始位置

### 不足
1. 查询范围局限于精力记录，未覆盖所有Capture note
2. 搜索功能相对简单，不支持复杂查询
3. 缺少统计分析功能
4. 界面布局可以进一步优化

## 🎨 新功能设计

### 1. 全量Capture Note查询
- 扫描所有Daily Notes的Capture note部分
- 支持按领域筛选：【系统】【健康】【精神】【财富】【传承】【生活】
- 支持按记录类型筛选：事件、想法、阅读、输出等

### 2. 高级搜索引擎
- 多关键词搜索（AND/OR逻辑）
- 支持正则表达式搜索
- 支持排除关键词（NOT逻辑）
- 支持模糊匹配和精确匹配

### 3. 时间维度分析
- 按日期范围筛选
- 按周/月/季度统计
- 时间趋势分析图表

### 4. 智能分类统计
- 各领域记录数量统计
- 高频关键词分析
- 活跃时间段分析

## 🛠️ 技术实现要点

### 数据获取策略
```javascript
// 获取所有Daily Notes的Capture note内容
const getAllCaptureNotes = async () => {
    const dailyNotes = await dv.pages('"0_Bullet Journal/Daily Notes"');
    const captureNotes = [];
    
    for (const note of dailyNotes) {
        const content = await dv.io.load(note.file.path);
        const captureSection = extractCaptureSection(content);
        if (captureSection) {
            captureNotes.push({
                date: note.file.name,
                path: note.file.path,
                content: captureSection
            });
        }
    }
    return captureNotes;
};
```

### 搜索算法设计
- 使用正则表达式进行模式匹配
- 实现分词和关键词高亮
- 支持权重评分和相关性排序

### 界面交互设计
- 响应式布局适配不同屏幕
- 实时搜索结果更新
- 键盘快捷键支持

## 📊 预期效果

### 功能提升
1. 查询覆盖率从精力记录扩展到全部Capture note
2. 搜索准确率和效率显著提升
3. 增加数据分析和可视化功能

### 用户体验提升
1. 操作更加直观便捷
2. 结果展示更加丰富
3. 支持多种查询方式

### 数据价值提升
1. 更好地挖掘日记数据价值
2. 发现记录模式和趋势
3. 支持个人成长分析

## ⏰ 时间安排
- 阶段1-2：需求分析和设计（已完成）
- 阶段3：核心功能实现（预计2-3小时）
- 阶段4：界面优化（预计1-2小时）
- 阶段5：测试完善（预计1小时）

## 🎯 成功标准
1. 能够查询所有Capture note记录
2. 支持多种搜索和筛选方式
3. 界面友好，操作便捷
4. 查询性能良好，结果准确
5. 用户满意度高
