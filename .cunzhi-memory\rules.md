# 开发规范和规则

- 对话导出功能项目配置：chat文件夹路径为./chat/，包含四个子文件夹(important/projects/learning/daily)，使用标准模板conversation-template.md和quick-template.md，文件命名格式YYYYMMDD_主题.md，智能分类规则：重要决策→important，项目相关→projects，学习探索→learning，日常咨询→daily
- 对话导出指令重新定义：1)导出对话=完全原始对话记录，按界面显示格式包含User/Assistant/思考过程/工具调用等所有内容，类似图片中的格式；2)导出对话：[主题]=结构化整理版，使用conversation-template.md模板；3)保存重要对话=深度分析版，包含价值分析和经验总结，存储到important文件夹
- 对话导出指令重新定义：1)导出对话=完全原始对话记录，按界面显示格式包含User/Assistant/思考过程/工具调用等所有内容，类似图片中的格式；2)导出对话：[主题]=结构化整理版，使用conversation-template.md模板；3)保存重要对话=深度分析版，包含价值分析和经验总结，存储到important文件夹。对话导出的'完整版'应该包含从用户输入第一行开始的所有信息，参考文件[2025-05-29_09-59-如何找回重置后的聊天记录.md]显示了期望的完整格式标准。
- Obsidian界面状态管理规则：1)按钮显示逻辑：有搜索结果时显示导出按钮，无结果时隐藏；2)用户确认时机：文件操作(移动/删除)前必须确认，内容生成无需确认；3)状态反馈原则：操作开始显示进行中，成功显示绿色提示，失败显示红色错误；4)确认对话框使用场景：不可逆操作、影响文件结构、用户偏好选择；5)加载状态显示：超过1秒的操作需要进度指示。
- 搜索界面交互设计规则：1)实时反馈原则：搜索过程显示进度，结果更新及时响应；2)状态显示标准：搜索中显示动画，完成显示结果数量，失败显示错误原因；3)操作确认时机：导出文件无需确认，移动文件需要确认，删除操作必须确认；4)界面响应性：长时间操作(>1秒)显示进度条，短操作直接反馈结果；5)用户引导原则：首次使用显示帮助，操作失败提供解决方案。
- 寸止MCP智能拦截机制澄清：经过2025-07-13测试发现，寸止MCP的"智能拦截"并非自动拦截所有对话，而是与interactive_feedback MCP协同工作。寸止MCP主要负责项目记忆管理和规则存储，而interactive_feedback负责用户交互拦截。两者功能互补：寸止MCP=记忆管理+规则存储，interactive_feedback=用户反馈收集+关键节点拦截。正确的工作流程应该是：需要用户反馈时主动调用interactive_feedback，需要记忆管理时调用寸止MCP
- Claude Code配置规范：
1. 环境变量设置：CLAUDE_CODE_MAX_OUTPUT_TOKENS=64000, ANTHROPIC_BASE_URL=https://code.wenwen-ai.com/v1, ANTHROPIC_AUTH_TOKEN=用户密钥
2. 文件清理原则：掌握命令行操作后，及时清理自动化脚本，包括安装脚本(.ps1)、批处理文件(.bat)
3. 故障排除流程：API配额问题→检查账户余额→重新创建令牌→联系官方客服→准备备选方案
4. 技能发展路径：脚本辅助阶段→命令行熟练阶段→独立操作阶段→项目管理优化阶段
5. 工具链备份：主要工具遇到问题时，准备Gemini等替代API服务，确保开发工作连续性
- CLI工具配置标准化规范：
1. 环境变量命名：遵循工具官方约定，如ANTHROPIC_BASE_URL、ANTHROPIC_AUTH_TOKEN等，保持一致性
2. 配置作用域选择：User级别适用于个人开发环境，Machine级别适用于服务器或共享环境
3. 配置文件结构：支持多种配置方式(环境变量、PowerShell Profile、批处理文件、JSON配置文件)以适应不同场景
4. 错误处理机制：配置过程中包含try-catch错误处理，提供清晰的错误信息和解决建议
5. 配置验证流程：设置后立即验证配置有效性，包括格式检查、连接测试、权限验证
6. 文档化要求：每个配置参数需要明确说明作用、默认值、修改建议和安全注意事项
- PowerShell应用安装规范：使用Add-AppPackage命令安装UWP应用时必须提供完整绝对路径，安装过程会显示进度条指示器，返回码0表示安装成功，支持离线部署适合企业环境，安装顺序必须遵循依赖关系先安装框架再安装主程序
- 数据安全备份机制：已创建Augment Agent全局记忆备份文档(./backup/Augment-Agent全局记忆备份-20250814.md)，包含Remember功能中的所有重要工作偏好、协作原则、技术配置等内容。建议定期更新备份，将核心偏好同步到寸止MCP和.augment-guidelines中，建立多层次的记忆保护机制
- Remember功能备用测试：用户偏好中文交流，生成总结性Markdown文档，不生成测试脚本，需要时协助编译和运行，关注数据安全性
- 翻译规则模板备用：当用户说"翻译 XXX文件"时自动应用规则：1)仅翻译英文文本保持代码片段技术术语文件路径命令行指令YAML元数据不变 2)确保翻译准确自然流畅符合中文表达习惯 3)保持完整Markdown格式结构 4)保存为原文件名_zh.md格式到当前工作目录 5)工作流程读取原文件→翻译→保存→确认完成
- 对话导出约定备用：导出对话=完整原始对话记录按界面显示格式包含User/Assistant/思考过程/工具调用等所有内容，导出对话：主题=结构化整理版使用conversation-template.md模板，保存重要对话=深度分析版包含价值分析和经验总结存储到important文件夹
