#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接生成AI风格番茄钟推广图JPG
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

def generate_ai_style_jpg():
    """生成AI风格JPG图片"""
    try:
        # 设置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--window-size=1200,1800')
        chrome_options.add_argument('--force-device-scale-factor=1')
        
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        
        # 设置窗口大小
        driver.set_window_size(1200, 1800)
        
        # 获取HTML文件路径
        html_file = os.path.abspath("番茄钟推广图_AI风格.html")
        file_url = f"file:///{html_file.replace(os.sep, '/')}"
        
        print(f"正在打开: {file_url}")
        driver.get(file_url)
        
        # 等待页面加载
        time.sleep(5)
        
        # 截图保存
        output_file = "番茄钟推广图_AI风格.jpg"
        driver.save_screenshot(output_file)
        
        driver.quit()
        
        if os.path.exists(output_file):
            print(f"✅ 成功生成: {output_file}")
            print(f"📁 文件大小: {os.path.getsize(output_file)} bytes")
            return True
        else:
            print("❌ 截图失败")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def main():
    """主函数"""
    print("🎨 开始生成AI风格番茄钟推广图...")
    
    # 检查HTML文件是否存在
    html_file = "番茄钟推广图_AI风格.html"
    if not os.path.exists(html_file):
        print(f"❌ HTML文件不存在: {html_file}")
        return
    
    print(f"✅ 找到HTML文件: {html_file}")
    print(f"📁 当前目录: {os.getcwd()}")
    
    # 生成JPG
    if generate_ai_style_jpg():
        print("🎉 AI风格推广图生成完成！")
    else:
        print("❌ 生成失败，请检查Chrome浏览器是否已安装")
        print("💡 建议：手动在浏览器中打开HTML文件并截图")

if __name__ == "__main__":
    main()
