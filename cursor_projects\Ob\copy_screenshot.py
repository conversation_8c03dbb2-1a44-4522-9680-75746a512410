#!/usr/bin/env python3
"""
复制Playwright生成的截图到当前目录
"""

import os
import shutil
import glob
from pathlib import Path

def find_and_copy_screenshot():
    """查找并复制最新的截图文件"""
    
    # Playwright临时目录路径
    temp_base = r"C:\Users\<USER>\AppData\Local\Temp\playwright-mcp-output"
    
    # 查找所有可能的截图文件
    screenshot_files = []
    
    if os.path.exists(temp_base):
        # 遍历所有时间戳目录
        for timestamp_dir in os.listdir(temp_base):
            timestamp_path = os.path.join(temp_base, timestamp_dir)
            if os.path.isdir(timestamp_path):
                # 查找JPG文件
                jpg_files = glob.glob(os.path.join(timestamp_path, "*.jpg"))
                for jpg_file in jpg_files:
                    if "MCP配置报告" in jpg_file or "MCP" in os.path.basename(jpg_file):
                        screenshot_files.append(jpg_file)
    
    if not screenshot_files:
        print("❌ 未找到截图文件")
        return False
    
    # 选择最新的文件
    latest_file = max(screenshot_files, key=os.path.getmtime)
    
    # 目标文件路径
    target_file = "MCP配置报告推广海报.jpg"
    
    try:
        # 复制文件
        shutil.copy2(latest_file, target_file)
        
        # 检查文件大小
        file_size = os.path.getsize(target_file) / 1024  # KB
        
        print(f"✅ 成功复制截图文件")
        print(f"📁 源文件: {latest_file}")
        print(f"📁 目标文件: {os.path.abspath(target_file)}")
        print(f"📊 文件大小: {file_size:.1f} KB")
        
        return True
        
    except Exception as e:
        print(f"❌ 复制失败: {str(e)}")
        return False

def main():
    print("📸 MCP配置报告截图复制工具")
    print("=" * 40)
    
    success = find_and_copy_screenshot()
    
    if success:
        print("\n🎉 JPG文件生成完成！")
        print("📂 文件位置: MCP配置报告推广海报.jpg")
    else:
        print("\n💡 如果没有找到文件，请手动截图：")
        print("1. 在浏览器中打开 MCP配置报告推广海报.html")
        print("2. 按F12打开开发者工具")
        print("3. 按Ctrl+Shift+P，输入'screenshot'")
        print("4. 选择'Capture full size screenshot'")
        print("5. 将下载的图片重命名为'MCP配置报告推广海报.jpg'")

if __name__ == "__main__":
    main()
