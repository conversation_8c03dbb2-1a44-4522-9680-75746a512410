<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Capture Notes 全局查询系统 - 智能知识管理解决方案</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
        }

        .main-title {
            font-size: 3.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .subtitle {
            font-size: 1.4rem;
            color: #a0a0a0;
            margin-bottom: 30px;
        }

        .badge {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .bento-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-template-rows: repeat(8, 120px);
            gap: 20px;
            margin-bottom: 60px;
        }

        .bento-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .bento-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(102, 126, 234, 0.5);
        }

        .bento-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .bento-item:hover::before {
            opacity: 1;
        }

        /* 主要功能区域 */
        .hero-feature {
            grid-column: span 8;
            grid-row: span 3;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
        }

        .quick-search {
            grid-column: span 4;
            grid-row: span 2;
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(32, 201, 151, 0.2) 100%);
        }

        .smart-filter {
            grid-column: span 4;
            grid-row: span 2;
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 152, 0, 0.2) 100%);
        }

        .scroll-view {
            grid-column: span 6;
            grid-row: span 2;
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.2) 0%, rgba(233, 30, 99, 0.2) 100%);
        }

        .statistics {
            grid-column: span 6;
            grid-row: span 2;
            background: linear-gradient(135deg, rgba(111, 66, 193, 0.2) 0%, rgba(156, 39, 176, 0.2) 100%);
        }

        .tech-specs {
            grid-column: span 4;
            grid-row: span 3;
            background: linear-gradient(135deg, rgba(23, 162, 184, 0.2) 0%, rgba(13, 110, 253, 0.2) 100%);
        }

        .user-experience {
            grid-column: span 8;
            grid-row: span 1;
            background: linear-gradient(135deg, rgba(108, 117, 125, 0.2) 0%, rgba(73, 80, 87, 0.2) 100%);
        }

        .item-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .item-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 15px;
            color: #ffffff;
        }

        .item-description {
            font-size: 1rem;
            color: #b0b0b0;
            line-height: 1.6;
        }

        .feature-list {
            list-style: none;
            margin-top: 20px;
        }

        .feature-list li {
            padding: 8px 0;
            position: relative;
            padding-left: 25px;
        }

        .feature-list li::before {
            content: '✨';
            position: absolute;
            left: 0;
            color: #667eea;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #a0a0a0;
        }

        .tech-tag {
            display: inline-block;
            background: rgba(102, 126, 234, 0.3);
            color: #ffffff;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.85rem;
            margin: 5px 5px 5px 0;
            border: 1px solid rgba(102, 126, 234, 0.5);
        }

        .cta-section {
            text-align: center;
            padding: 60px 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border-radius: 30px;
            margin-top: 40px;
        }

        .cta-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .cta-description {
            font-size: 1.2rem;
            color: #a0a0a0;
            margin-bottom: 30px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .highlight-box {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
            border: 1px solid rgba(102, 126, 234, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }

        .workflow-steps {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .workflow-step {
            flex: 1;
            text-align: center;
            padding: 20px;
            margin: 0 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            min-width: 200px;
        }

        .step-number {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            line-height: 40px;
            font-weight: 700;
            margin-bottom: 15px;
        }

        @media (max-width: 768px) {
            .bento-grid {
                grid-template-columns: 1fr;
                grid-template-rows: auto;
            }

            .bento-item {
                grid-column: span 1 !important;
                grid-row: span 1 !important;
            }

            .main-title {
                font-size: 2.5rem;
            }

            .workflow-steps {
                flex-direction: column;
            }

            .workflow-step {
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <div class="header">
            <h1 class="main-title">📝 Capture Notes 全局查询系统</h1>
            <p class="subtitle">智能知识管理 · 高效信息检索 · 数据可视化分析</p>
            <div class="badge">🚀 Obsidian 知识管理解决方案</div>
        </div>

        <!-- Bento Grid 主要内容区域 -->
        <div class="bento-grid">
            <!-- 主要功能介绍 -->
            <div class="bento-item hero-feature">
                <span class="item-icon">🎯</span>
                <h2 class="item-title">全量智能查询系统</h2>
                <p class="item-description">
                    覆盖所有Daily Notes中的Capture note记录，支持多维度筛选和智能搜索。
                    从海量笔记中秒速定位目标信息，让知识管理变得轻松高效。
                </p>
                <div class="highlight-box">
                    <strong>🔍 核心优势：</strong> 全文检索 + 精确匹配 + 模糊搜索，三种模式随心切换
                </div>
                <ul class="feature-list">
                    <li>支持AND/OR/EXACT三种逻辑运算模式</li>
                    <li>多关键词空格分隔，智能语义理解</li>
                    <li>实时搜索结果高亮显示</li>
                </ul>
            </div>

            <!-- 快速搜索 -->
            <div class="bento-item quick-search">
                <span class="item-icon">⚡</span>
                <h3 class="item-title">快速搜索</h3>
                <p class="item-description">
                    顶部一键搜索面板，预设热门关键词按钮
                </p>
                <div class="tech-tag">生活</div>
                <div class="tech-tag">知识管理</div>
                <div class="tech-tag">Obsidian</div>
                <div class="tech-tag">Prompt</div>
                <div class="tech-tag">输出</div>
                <div class="tech-tag">模板</div>
            </div>

            <!-- 智能筛选 -->
            <div class="bento-item smart-filter">
                <span class="item-icon">🎛️</span>
                <h3 class="item-title">智能筛选</h3>
                <p class="item-description">
                    多维度筛选条件，精准定位目标内容
                </p>
                <ul class="feature-list">
                    <li>按领域筛选：系统、健康、精神等</li>
                    <li>按时间筛选：最近7天、30天、90天</li>
                    <li>健康细分：精力、调理、中医等</li>
                </ul>
            </div>

            <!-- 滚动查看 -->
            <div class="bento-item scroll-view">
                <span class="item-icon">📱</span>
                <h3 class="item-title">滚动查看</h3>
                <p class="item-description">
                    350px滚动容器，大量数据也能丝滑浏览。智能数量提示，超过5条记录自动引导滚动查看。
                </p>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">350px</div>
                        <div class="stat-label">滚动高度</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">5+</div>
                        <div class="stat-label">智能提示</div>
                    </div>
                </div>
            </div>

            <!-- 统计分析 -->
            <div class="bento-item statistics">
                <span class="item-icon">📊</span>
                <h3 class="item-title">可视化统计</h3>
                <p class="item-description">
                    综合统计卡片，总体数据、月度趋势、活跃时间段三合一展示
                </p>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">6个月</div>
                        <div class="stat-label">趋势分析</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">Top5</div>
                        <div class="stat-label">活跃时段</div>
                    </div>
                </div>
            </div>

            <!-- 技术规格 -->
            <div class="bento-item tech-specs">
                <span class="item-icon">⚙️</span>
                <h3 class="item-title">技术特性</h3>
                <p class="item-description">
                    基于Dataview API构建，纯JavaScript实现，响应式设计
                </p>
                <div class="tech-tag">Dataview API</div>
                <div class="tech-tag">JavaScript ES6+</div>
                <div class="tech-tag">响应式布局</div>
                <div class="tech-tag">事件委托</div>
                <div class="tech-tag">异步处理</div>
                <div class="tech-tag">正则匹配</div>
                <div class="tech-tag">DOM操作</div>
                <div class="tech-tag">CSS Grid</div>
            </div>

            <!-- 用户体验 -->
            <div class="bento-item user-experience">
                <span class="item-icon">✨</span>
                <h3 class="item-title">极致用户体验</h3>
                <p class="item-description">
                    紧凑布局 · 一键操作 · 日期跳转 · 高亮显示 · 实时反馈
                </p>
            </div>
        </div>

        <!-- 工作流程展示 -->
        <div class="cta-section">
            <h2 class="cta-title">🚀 三步开启智能查询</h2>
            <p class="cta-description">
                简单三步，即可体验强大的知识管理功能，让您的Obsidian笔记系统焕然一新
            </p>

            <div class="workflow-steps">
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <h4>快速搜索</h4>
                    <p>点击顶部预设按钮或输入关键词</p>
                </div>
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <h4>智能筛选</h4>
                    <p>设置领域、时间等筛选条件</p>
                </div>
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <h4>浏览结果</h4>
                    <p>滚动查看结果，点击日期跳转</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
