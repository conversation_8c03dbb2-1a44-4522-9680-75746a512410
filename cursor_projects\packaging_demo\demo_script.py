#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示脚本：系统信息查看器
用于演示Python脚本打包过程
"""

import os
import sys
import platform
import psutil
import json
from datetime import datetime
from pathlib import Path

class SystemInfoViewer:
    """系统信息查看器类"""
    
    def __init__(self):
        self.info = {}
        self.collect_system_info()
    
    def collect_system_info(self):
        """收集系统信息"""
        try:
            # 基本系统信息
            self.info['system'] = {
                'platform': platform.system(),
                'platform_release': platform.release(),
                'platform_version': platform.version(),
                'architecture': platform.machine(),
                'hostname': platform.node(),
                'processor': platform.processor(),
                'python_version': platform.python_version(),
            }
            
            # CPU信息
            self.info['cpu'] = {
                'physical_cores': psutil.cpu_count(logical=False),
                'total_cores': psutil.cpu_count(logical=True),
                'max_frequency': f"{psutil.cpu_freq().max:.2f}Mhz" if psutil.cpu_freq() else "N/A",
                'current_frequency': f"{psutil.cpu_freq().current:.2f}Mhz" if psutil.cpu_freq() else "N/A",
                'cpu_usage': f"{psutil.cpu_percent(interval=1)}%"
            }
            
            # 内存信息
            svmem = psutil.virtual_memory()
            self.info['memory'] = {
                'total': self.get_size(svmem.total),
                'available': self.get_size(svmem.available),
                'used': self.get_size(svmem.used),
                'percentage': f"{svmem.percent}%"
            }
            
            # 磁盘信息
            self.info['disk'] = {}
            partitions = psutil.disk_partitions()
            for partition in partitions:
                try:
                    partition_usage = psutil.disk_usage(partition.mountpoint)
                    self.info['disk'][partition.device] = {
                        'mountpoint': partition.mountpoint,
                        'file_system': partition.fstype,
                        'total_size': self.get_size(partition_usage.total),
                        'used': self.get_size(partition_usage.used),
                        'free': self.get_size(partition_usage.free),
                        'percentage': f"{(partition_usage.used/partition_usage.total)*100:.1f}%"
                    }
                except PermissionError:
                    continue
            
            # 网络信息
            self.info['network'] = {}
            net_io = psutil.net_io_counters()
            self.info['network']['total'] = {
                'bytes_sent': self.get_size(net_io.bytes_sent),
                'bytes_received': self.get_size(net_io.bytes_recv)
            }
            
        except Exception as e:
            self.info['error'] = str(e)
    
    def get_size(self, bytes, suffix="B"):
        """将字节转换为人类可读的格式"""
        factor = 1024
        for unit in ["", "K", "M", "G", "T", "P"]:
            if bytes < factor:
                return f"{bytes:.2f}{unit}{suffix}"
            bytes /= factor
    
    def display_info(self):
        """显示系统信息"""
        print("=" * 60)
        print("           系统信息查看器")
        print("=" * 60)
        print(f"扫描时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 系统信息
        print("🖥️  系统信息:")
        for key, value in self.info['system'].items():
            print(f"   {key.replace('_', ' ').title()}: {value}")
        print()
        
        # CPU信息
        print("🔧 CPU信息:")
        for key, value in self.info['cpu'].items():
            print(f"   {key.replace('_', ' ').title()}: {value}")
        print()
        
        # 内存信息
        print("💾 内存信息:")
        for key, value in self.info['memory'].items():
            print(f"   {key.replace('_', ' ').title()}: {value}")
        print()
        
        # 磁盘信息
        print("💿 磁盘信息:")
        for device, info in self.info['disk'].items():
            print(f"   设备: {device}")
            for key, value in info.items():
                print(f"     {key.replace('_', ' ').title()}: {value}")
            print()
        
        # 网络信息
        print("🌐 网络信息:")
        for key, value in self.info['network']['total'].items():
            print(f"   {key.replace('_', ' ').title()}: {value}")
        print()
    
    def save_to_file(self, filename="system_info.json"):
        """保存信息到文件"""
        try:
            # 获取正确的保存路径
            if hasattr(sys, '_MEIPASS'):
                # 打包后的环境
                save_path = os.path.join(os.path.dirname(sys.executable), filename)
            else:
                # 开发环境
                save_path = filename
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(self.info, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 系统信息已保存到: {save_path}")
            return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False

def main():
    """主函数"""
    try:
        print("正在收集系统信息...")
        viewer = SystemInfoViewer()
        
        while True:
            print("\n" + "=" * 40)
            print("请选择操作:")
            print("1. 显示系统信息")
            print("2. 保存信息到文件")
            print("3. 刷新信息")
            print("0. 退出程序")
            print("=" * 40)
            
            choice = input("请输入选择 (0-3): ").strip()
            
            if choice == '1':
                viewer.display_info()
            elif choice == '2':
                viewer.save_to_file()
            elif choice == '3':
                print("正在刷新系统信息...")
                viewer = SystemInfoViewer()
                print("✅ 信息已刷新")
            elif choice == '0':
                print("感谢使用！再见！")
                break
            else:
                print("❌ 无效选择，请重新输入")
            
            input("\n按回车键继续...")
    
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        input("按回车键退出...")

if __name__ == "__main__":
    main()
