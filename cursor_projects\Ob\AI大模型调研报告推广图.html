<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI大模型调研报告-2025年6月版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: #1a1d29;
            color: #ffffff;
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 60px 40px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 80px;
        }
        
        .main-title {
            font-size: 3.5rem;
            font-weight: 700;
            color: #00ff88;
            margin-bottom: 15px;
            letter-spacing: -1px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #8892b0;
            margin-bottom: 20px;
            font-weight: 400;
        }
        
        .description {
            font-size: 1rem;
            color: #64748b;
            margin-bottom: 50px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .stats-section {
            display: flex;
            justify-content: center;
            gap: 80px;
            margin-bottom: 80px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #00ff88;
            margin-bottom: 8px;
            display: block;
        }
        
        .stat-label {
            font-size: 1rem;
            color: #8892b0;
            font-weight: 500;
        }
        
        .bento-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            grid-auto-rows: minmax(250px, auto);
        }
        
        .bento-card {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 20px;
            padding: 35px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .bento-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color);
            border-radius: 20px 20px 0 0;
        }
        
        .bento-card:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.05);
            border-color: var(--card-color);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .card-large {
            grid-column: span 2;
            grid-row: span 2;
        }
        
        .card-wide {
            grid-column: span 2;
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .card-icon {
            font-size: 2.5rem;
            margin-right: 15px;
            color: var(--card-color);
        }
        
        .card-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #ffffff;
        }
        
        .card-subtitle {
            font-size: 0.9rem;
            color: #8892b0;
            margin-top: 5px;
        }
        
        .model-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        
        .model-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .model-item:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: translateY(-2px);
        }
        
        .model-name {
            font-weight: 600;
            color: var(--card-color);
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .model-desc {
            font-size: 0.8rem;
            color: #a0a0a0;
        }
        
        .features-list {
            list-style: none;
            margin-bottom: 20px;
        }
        
        .features-list li {
            padding: 8px 0;
            color: #cbd5e1;
            font-size: 0.9rem;
            position: relative;
            padding-left: 20px;
        }
        
        .features-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--card-color);
            font-weight: 600;
        }
        
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .tag {
            background: rgba(255, 255, 255, 0.1);
            color: var(--card-color);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .reliability-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .high-reliability {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
        
        .medium-reliability {
            background: rgba(234, 179, 8, 0.2);
            color: #eab308;
            border: 1px solid rgba(234, 179, 8, 0.3);
        }
        
        .comparison-table {
            margin-top: 20px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            padding: 20px;
        }
        
        .comparison-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .comparison-row:last-child {
            border-bottom: none;
        }
        
        .comparison-label {
            font-size: 0.9rem;
            color: #cbd5e1;
        }
        
        .comparison-value {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--card-color);
        }
        
        /* 卡片颜色主题 */
        .card-overview { --card-color: #00ff88; }
        .card-ranking { --card-color: #3b82f6; }
        .card-chinese { --card-color: #06b6d4; }
        .card-programming { --card-color: #8b5cf6; }
        .card-reasoning { --card-color: #f97316; }
        .card-comparison { --card-color: #ec4899; }
        .card-trends { --card-color: #eab308; }
        .card-recommendations { --card-color: #ef4444; }
        
        @media (max-width: 1024px) {
            .bento-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
            }
            
            .card-large,
            .card-wide {
                grid-column: span 1;
            }
            
            .stats-section {
                gap: 50px;
            }
        }
        
        @media (max-width: 768px) {
            .bento-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .stats-section {
                gap: 30px;
            }
            
            .main-title {
                font-size: 2.5rem;
            }
            
            .container {
                padding: 40px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">AI大模型调研报告</h1>
            <p class="subtitle">2025年6月版 · 让外行人也能快速看懂的AI大模型指南</p>
            <p class="description">基于权威评测数据 • 分级可靠性标注 • 实用选择建议 • 专业深度分析</p>
            
            <div class="stats-section">
                <div class="stat-item">
                    <span class="stat-number">20+</span>
                    <span class="stat-label">主流模型</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">8</span>
                    <span class="stat-label">应用场景</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">5</span>
                    <span class="stat-label">工作流程</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">2025.6</span>
                    <span class="stat-label">最新版本</span>
                </div>
            </div>
        </div>
        
        <div class="bento-grid">
            <!-- 核心发现 - 大卡片 -->
            <div class="bento-card card-large card-overview">
                <div class="card-header">
                    <div class="card-icon">🏆</div>
                    <div>
                        <div class="card-title">2025年核心发现</div>
                        <div class="card-subtitle">AI大模型发展四大趋势</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li>中国模型崛起 <span class="reliability-badge high-reliability">🟢 高可靠</span></li>
                    <li>推理能力突破 <span class="reliability-badge high-reliability">🟢 高可靠</span></li>
                    <li>成本大幅下降 <span class="reliability-badge medium-reliability">🟡 中等可靠</span></li>
                    <li>专业化趋势明显 <span class="reliability-badge high-reliability">🟢 高可靠</span></li>
                </ul>
                
                <div class="model-grid">
                    <div class="model-item">
                        <div class="model-name">编程之王</div>
                        <div class="model-desc">Claude 4 Opus - 编程能力极强</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">推理之王</div>
                        <div class="model-desc">OpenAI o3-pro - 推理能力顶级</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">综合之王</div>
                        <div class="model-desc">GPT-4o - 综合性能最强</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">创新先锋</div>
                        <div class="model-desc">Claude 3.7 Sonnet - 混合推理</div>
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">权威评测</span>
                    <span class="tag">分级标注</span>
                    <span class="tag">实用指南</span>
                    <span class="tag">深度分析</span>
                </div>
            </div>
            
            <!-- 国际顶级模型排行 -->
            <div class="bento-card card-ranking">
                <div class="card-header">
                    <div class="card-icon">🌟</div>
                    <div>
                        <div class="card-title">国际顶级模型排行</div>
                        <div class="card-subtitle">闭源模型性能榜单</div>
                    </div>
                </div>
                
                <div class="model-grid">
                    <div class="model-item">
                        <div class="model-name">1. Claude 4 Opus</div>
                        <div class="model-desc">编程能力极强，长时间编码</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">2. OpenAI o3-pro</div>
                        <div class="model-desc">推理能力顶级，数学测试优秀</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">3. Claude 4 Sonnet</div>
                        <div class="model-desc">高性能平衡型，编程优秀</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">4. GPT-4o</div>
                        <div class="model-desc">综合能力最强，多模态处理</div>
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">官方发布</span>
                    <span class="tag">权威测试</span>
                    <span class="tag">性能排行</span>
                </div>
            </div>
            
            <!-- 中国领先模型 -->
            <div class="bento-card card-chinese">
                <div class="card-header">
                    <div class="card-icon">🇨🇳</div>
                    <div>
                        <div class="card-title">中国领先模型</div>
                        <div class="card-subtitle">国产AI崛起之路</div>
                    </div>
                </div>
                
                <div class="model-grid">
                    <div class="model-item">
                        <div class="model-name">Qwen2.5-Max</div>
                        <div class="model-desc">数学编程双冠王</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">DeepSeek V3</div>
                        <div class="model-desc">开源第一、性价比高</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">文心一言 4.0</div>
                        <div class="model-desc">中文理解优秀</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">腾讯混元</div>
                        <div class="model-desc">首次进入全球前15</div>
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">国产崛起</span>
                    <span class="tag">开源优势</span>
                    <span class="tag">中文优化</span>
                </div>
            </div>
            
            <!-- 编程能力对比 - 宽卡片 -->
            <div class="bento-card card-wide card-programming">
                <div class="card-header">
                    <div class="card-icon">💻</div>
                    <div>
                        <div class="card-title">编程能力专项对比</div>
                        <div class="card-subtitle">基于HumanEval等权威测试</div>
                    </div>
                </div>
                
                <div class="comparison-table">
                    <div class="comparison-row">
                        <span class="comparison-label">Claude 4 Opus</span>
                        <span class="comparison-value">卓越 ⭐⭐⭐⭐⭐</span>
                    </div>
                    <div class="comparison-row">
                        <span class="comparison-label">GPT-4o (HumanEval)</span>
                        <span class="comparison-value">88.7% 优秀</span>
                    </div>
                    <div class="comparison-row">
                        <span class="comparison-label">Claude 3.7 Sonnet</span>
                        <span class="comparison-value">79.6% 优秀</span>
                    </div>
                    <div class="comparison-row">
                        <span class="comparison-label">Qwen2.5-Coder-32B</span>
                        <span class="comparison-value">开源编程专用</span>
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">HumanEval测试</span>
                    <span class="tag">代码质量</span>
                    <span class="tag">多语言支持</span>
                    <span class="tag">专业评估</span>
                </div>
            </div>
            
            <!-- 推理能力对比 -->
            <div class="bento-card card-reasoning">
                <div class="card-header">
                    <div class="card-icon">🧠</div>
                    <div>
                        <div class="card-title">推理能力专项对比</div>
                        <div class="card-subtitle">数学推理与逻辑分析</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li>OpenAI o3-pro - 推理能力顶级，AIME测试优秀</li>
                    <li>OpenAI o3 - 推理专用，问题解决能力强</li>
                    <li>Claude 3.7 Sonnet - 混合推理，深度+快速</li>
                    <li>DeepSeek R1系列 - 推理优化，思维链清晰</li>
                </ul>
                
                <div style="margin-top: 15px; padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px;">
                    <div style="font-size: 0.9rem; color: #f97316; font-weight: 600;">GSM8K数学测试</div>
                    <div style="font-size: 0.8rem; color: #a0a0a0; margin-top: 5px;">
                        GPT-4o: 92.3% | Claude 3.7: 88.7%
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">AIME测试</span>
                    <span class="tag">数学推理</span>
                    <span class="tag">逻辑分析</span>
                </div>
            </div>
            
            <!-- GPT-4o vs Claude 3.7 对比 -->
            <div class="bento-card card-comparison">
                <div class="card-header">
                    <div class="card-icon">⚔️</div>
                    <div>
                        <div class="card-title">GPT-4o vs Claude 3.7</div>
                        <div class="card-subtitle">权威性能数据对比</div>
                    </div>
                </div>
                
                <div class="comparison-table">
                    <div class="comparison-row">
                        <span class="comparison-label">MMLU综合理解</span>
                        <span class="comparison-value">90.5% vs 87.2%</span>
                    </div>
                    <div class="comparison-row">
                        <span class="comparison-label">HumanEval编程</span>
                        <span class="comparison-value">88.7% vs 79.6%</span>
                    </div>
                    <div class="comparison-row">
                        <span class="comparison-label">GSM8K数学</span>
                        <span class="comparison-value">92.3% vs 88.7%</span>
                    </div>
                    <div class="comparison-row">
                        <span class="comparison-label">独特优势</span>
                        <span class="comparison-value">多模态 vs 混合推理</span>
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">权威评测</span>
                    <span class="tag">性能对比</span>
                    <span class="tag">选择建议</span>
                </div>
            </div>
            
            <!-- 2025年发展趋势 -->
            <div class="bento-card card-trends">
                <div class="card-header">
                    <div class="card-icon">📈</div>
                    <div>
                        <div class="card-title">2025年发展趋势</div>
                        <div class="card-subtitle">AI大模型未来方向</div>
                    </div>
                </div>
                
                <ul class="features-list">
                    <li>推理模型崛起 - 专门解决复杂问题</li>
                    <li>开源追赶闭源 - 性能提升成本降低</li>
                    <li>中国模型国际化 - 国际榜单表现优异</li>
                    <li>专业化分工 - 不同领域深度优化</li>
                </ul>
                
                <div style="margin-top: 15px; padding: 15px; background: rgba(234, 179, 8, 0.1); border-radius: 8px; border-left: 3px solid #eab308;">
                    <strong>重要提醒:</strong> AI发展迅速，需要持续关注新模型，合理控制成本，注意数据安全
                </div>
                
                <div class="tags">
                    <span class="tag">趋势预测</span>
                    <span class="tag">技术发展</span>
                    <span class="tag">市场变化</span>
                </div>
            </div>
            
            <!-- 选择建议 -->
            <div class="bento-card card-recommendations">
                <div class="card-header">
                    <div class="card-icon">🎯</div>
                    <div>
                        <div class="card-title">用户选择建议</div>
                        <div class="card-subtitle">针对不同需求的推荐</div>
                    </div>
                </div>
                
                <div class="model-grid">
                    <div class="model-item">
                        <div class="model-name">👨‍💼 商务人士</div>
                        <div class="model-desc">Claude 4 Sonnet + GPT-4o</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">👨‍💻 程序员</div>
                        <div class="model-desc">Claude 4 Opus + Qwen2.5-Coder</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">🎓 学生/研究者</div>
                        <div class="model-desc">Claude 3.7 Sonnet + DeepSeek V3</div>
                    </div>
                    <div class="model-item">
                        <div class="model-name">🏢 企业用户</div>
                        <div class="model-desc">私有部署 + API调用</div>
                    </div>
                </div>
                
                <div class="tags">
                    <span class="tag">用户分类</span>
                    <span class="tag">成本考虑</span>
                    <span class="tag">实用建议</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
