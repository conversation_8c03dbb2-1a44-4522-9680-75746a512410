# PowerShell 命令行基础知识

> 📅 创建时间：2025-08-15 星期五  
> 📝 文档类型：技术知识整理  
> 🎯 适用对象：PowerShell 初学者和日常使用者

## 📋 目录

- [基础概念](#基础概念)
- [文件和目录操作](#文件和目录操作)
- [文件内容操作](#文件内容操作)
- [搜索和查找](#搜索和查找)
- [系统信息](#系统信息)
- [网络相关](#网络相关)
- [实用工具](#实用工具)
- [包管理](#包管理)
- [快捷键和技巧](#快捷键和技巧)
- [常见误区](#常见误区)

## 🎯 基础概念

### 什么是 PowerShell
PowerShell 是 Microsoft 开发的命令行界面和脚本语言，比传统的 CMD 更强大，支持面向对象的操作。

### 基本语法特点
- 命令不区分大小写
- 支持 Tab 键自动补全
- 支持命令别名（如 `dir` 是 `Get-ChildItem` 的别名）
- 支持管道操作 `|`

## 📁 文件和目录操作

### 查看目录内容
```powershell
# 基本列表命令
dir                          # 列出当前目录内容
ls                           # Linux 风格别名
Get-ChildItem               # PowerShell 原生命令

# 带参数的用法
dir *.txt                   # 只显示 .txt 文件
dir -Force                  # 显示隐藏文件
dir -Recurse               # 递归显示所有子目录
```

### 路径操作
```powershell
# 查看和切换路径
pwd                         # 显示当前路径
Get-Location               # PowerShell 原生命令
cd 路径                     # 切换到指定目录
cd ..                      # 返回上级目录
cd ~                       # 回到用户主目录
cd /                       # 回到根目录
```

**⚠️ 常见错误：**
```powershell
# ❌ 错误用法
cd "Get-Location"          # 这会尝试进入名为 "Get-Location" 的文件夹

# ✅ 正确用法
Get-Location               # 查看当前路径
cd "C:\某个路径"           # 切换到指定路径
```

### 文件和文件夹操作
```powershell
# 创建操作
mkdir 文件夹名              # 创建文件夹
New-Item -ItemType Directory -Name "文件夹名"  # PowerShell 原生方式
echo "内容" > 文件名.txt    # 创建文件并写入内容

# 复制和移动
copy 源文件 目标位置        # 复制文件
Copy-Item 源文件 目标位置   # PowerShell 原生命令
move 源文件 目标位置        # 移动文件
Move-Item 源文件 目标位置   # PowerShell 原生命令

# 删除操作
del 文件名                 # 删除文件
rm 文件名                  # Linux 风格别名
Remove-Item 文件名         # PowerShell 原生命令
rmdir 文件夹名             # 删除空文件夹
```

## 📄 文件内容操作

### 查看文件内容
```powershell
type 文件名                # 显示文件全部内容
cat 文件名                 # Linux 风格别名
Get-Content 文件名         # PowerShell 原生命令
more 文件名                # 分页显示文件内容
```

### 写入文件内容
```powershell
echo "内容" > 文件名       # 覆盖写入文件
echo "内容" >> 文件名      # 追加到文件末尾
"内容" | Out-File 文件名   # PowerShell 原生方式
```

### 编辑文件
```powershell
notepad 文件名             # 用记事本打开
code 文件名                # 用 VS Code 打开（如果已安装）
```

## 🔍 搜索和查找

### 查找程序和文件
```powershell
where 程序名               # 查找程序的安装位置
Get-Command 程序名         # 查找命令位置
findstr "关键词" 文件名    # 在文件中搜索文本
Select-String "关键词" 文件名  # PowerShell 原生搜索

# 递归查找文件
Get-ChildItem -Recurse -Name "*.txt"           # 查找所有 .txt 文件
Get-ChildItem -Recurse -Filter "*关键词*"     # 按文件名关键词查找
```

## 💻 系统信息

### 日期和时间
```powershell
Get-Date                                    # 显示当前日期时间
Get-Date -Format 'yyyy-MM-dd dddd'        # 格式化显示：2025-08-15 星期五
Get-Date -Format 'HH:mm:ss'               # 只显示时间
```

### 系统信息
```powershell
hostname                   # 显示计算机名
whoami                     # 显示当前用户
$env:USERNAME             # 获取用户名环境变量
$env:COMPUTERNAME         # 获取计算机名环境变量

# 进程和服务
Get-Process               # 显示所有运行进程
Get-Process -Name "notepad"  # 显示特定进程
Get-Service               # 显示系统服务
```

## 🌐 网络相关

```powershell
ping 网址或IP              # 测试网络连接
ping google.com           # 示例：测试到谷歌的连接
ipconfig                  # 显示网络配置信息
ipconfig /all             # 显示详细网络配置
netstat                   # 显示网络连接状态
netstat -an               # 显示所有连接和监听端口
```

## 🔧 实用工具

### 屏幕和历史
```powershell
cls                       # 清屏
clear                     # Linux 风格别名
Clear-Host               # PowerShell 原生命令

history                  # 显示命令历史
Get-History              # PowerShell 原生命令
```

### 帮助系统
```powershell
help 命令名               # 查看命令帮助
Get-Help 命令名           # PowerShell 原生帮助
Get-Command *关键词*      # 搜索包含关键词的命令
Get-Command -Verb Get     # 查找所有以 Get 开头的命令
```

## 📦 包管理

### 常用包管理器
```powershell
# Node.js 包管理
npm install 包名          # 安装 npm 包
npm list                  # 列出已安装的包

# Python 包管理
pip install 包名          # 安装 Python 包
pip list                  # 列出已安装的包

# Windows 包管理器
winget install 软件名     # 安装软件
winget list               # 列出已安装的软件
```

## ⌨️ 快捷键和技巧

### 常用快捷键
| 快捷键 | 功能 |
|--------|------|
| `Tab` | 自动补全命令或路径 |
| `↑` `↓` | 浏览命令历史 |
| `Ctrl+C` | 中断当前执行的命令 |
| `Ctrl+L` | 清屏（等同于 cls） |
| `Ctrl+R` | 搜索命令历史 |
| `F7` | 显示命令历史列表 |

### 实用技巧
```powershell
# 管道操作
Get-Process | Where-Object {$_.CPU -gt 100}  # 查找 CPU 使用率高的进程
dir | Sort-Object Length -Descending         # 按文件大小排序

# 环境变量
$env:PATH                 # 查看 PATH 环境变量
$env:USERNAME            # 查看当前用户名
```

## ⚠️ 常见误区

### 1. 命令组合错误
```powershell
# ❌ 错误
cd "Get-Location"        # 试图进入名为 "Get-Location" 的文件夹

# ✅ 正确
Get-Location             # 查看当前路径
cd "实际路径"            # 切换到实际路径
```

### 2. 执行策略问题
```powershell
# 查看当前执行策略
Get-ExecutionPolicy

# 注意：Get-ExecutionPolicy 不是查看路径的命令
# 它是查看 PowerShell 脚本执行权限的命令
```

### 3. 路径中的空格
```powershell
# ❌ 可能出错
cd C:\Program Files      # 路径中有空格可能导致问题

# ✅ 推荐做法
cd "C:\Program Files"    # 用引号包围含空格的路径
```

## 📚 学习建议

1. **从基础开始**：先掌握 `dir`、`cd`、`pwd` 等基本命令
2. **多用 Tab 补全**：减少输入错误，提高效率
3. **善用帮助**：遇到不懂的命令就用 `help 命令名`
4. **实践为主**：在安全环境下多练习各种命令
5. **了解别名**：学会 PowerShell 原生命令和简短别名的对应关系

## 🔗 相关资源

- [Microsoft PowerShell 官方文档](https://docs.microsoft.com/powershell/)
- [PowerShell Gallery](https://www.powershellgallery.com/)
- 本地帮助：在 PowerShell 中输入 `help` 查看更多信息

---

*📝 备注：本文档基于 PowerShell 5.1+ 版本编写，适用于 Windows 10/11 环境。*
