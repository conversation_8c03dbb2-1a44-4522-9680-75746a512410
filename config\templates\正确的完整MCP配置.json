{"mcpServers": {"mcp-obsidian": {"command": "uv", "args": ["tool", "run", "mcp-obsidian", "--vault-path", "C:\\Users\\<USER>\\Desktop\\测试库"], "env": {"OBSIDIAN_API_KEY": "a712c31903d2c76f85bbf8acb44c4b5c6a497fbcd206557a9102175eebcef6b6"}}, "shrimp-task-manager": {"command": "npx", "args": ["shrimp-task-manager"]}, "context7": {"command": "npx", "args": ["context7"]}, "sequential-thinking": {"command": "npx", "args": ["sequential-thinking"]}, "playwright": {"command": "npx", "args": ["@executeautomation/playwright-mcp-server", "-y"], "env": {"PLAYWRIGHT_API_KEY": "07885b1c-a040-453f-8685-c0d6ee0336e8"}}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced"]}, "fetch": {"command": "python", "args": ["-m", "mcp_fetch"]}, "replicate-flux-mcp": {"command": "uvx", "args": ["replicate-flux-mcp"], "env": {"REPLICATE_API_TOKEN": "****************************************"}}, "together-image-gen": {"command": "uvx", "args": ["together-image-gen"], "env": {"TOGETHER_API_KEY": "aa188707-1019-4641-a6ea-ff43fa1daa9b"}}}}