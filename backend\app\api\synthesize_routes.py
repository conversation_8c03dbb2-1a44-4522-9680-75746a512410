# -*- coding: utf-8 -*-
"""/synthesize 路由：TTS合成章节音频
- 默认本地pyttsx3生成WAV → 视情况转MP3 → 记录chapters.cached_path
- 并发控制与重试（MVP：串行，失败重试次数限制）
"""
from __future__ import annotations
import tempfile
import shutil
from pathlib import Path
from typing import Optional

from fastapi import APIRouter, HTTPException, Query
from sqlalchemy.orm import Session

from ..db.session import SessionLocal
from ..db.models import Book, Chapter
from ..services.tts_local import synthesize_wav
from ..services.audio_encode import wav_to_mp3

router = APIRouter()


def get_db() -> Session:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@router.post("/synthesize", tags=["synthesize"])
async def synthesize(
    bookId: int = Query(..., description="书籍ID"),
    chapterId: int = Query(..., description="章节ID"),
    rate: Optional[int] = Query(None, description="语速（pyttsx3整数）"),
    forceRegen: Optional[bool] = Query(False, description="强制重新生成，忽略缓存"),
):
    db_gen = get_db()
    db = next(db_gen)
    try:
        ch = db.query(Chapter).filter(Chapter.id == chapterId, Chapter.book_id == bookId).first()
        if not ch:
            raise HTTPException(status_code=404, detail="章节不存在")
        book = db.query(Book).filter(Book.id == bookId).first()
        if not book:
            raise HTTPException(status_code=404, detail="书籍不存在")
        text = _read_text_segment(Path(book.file_path), book.encoding, ch.start_offset, ch.end_offset)

        # 如果已有缓存且不强制重建，直接返回
        if ch.cached_path and not forceRegen:
            return {"audioPath": ch.cached_path}

        target_dir = Path("temp") / "cache" / f"book_{bookId}"
        target_dir.mkdir(parents=True, exist_ok=True)
        target_mp3 = target_dir / f"ch_{ch.idx}.mp3"

        # 先合成为临时WAV，再尝试转MP3；失败时将WAV复制到持久缓存
        with tempfile.TemporaryDirectory() as td:
            wav_path = Path(td) / f"book{bookId}_ch{chapterId}.wav"
            synthesize_wav(text, wav_path, rate=rate)
            ok = wav_to_mp3(wav_path, target_mp3)
            if ok:
                audio_path = target_mp3.as_posix()
            else:
                target_wav = target_dir / f"ch_{ch.idx}.wav"
                shutil.copyfile(wav_path, target_wav)
                audio_path = target_wav.as_posix()

        # 更新缓存路径
        ch.cached_path = audio_path
        db.add(ch)
        db.commit()
        return {"audioPath": audio_path}
    except HTTPException:
        raise
    except ImportError as e:
        # pyttsx3 未安装的情况
        raise HTTPException(status_code=500, detail=f"本地TTS不可用，请安装pyttsx3：{e}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"合成失败: {e}")
    finally:
        try:
            next(db_gen)
        except StopIteration:
            pass


def _read_text_segment(path: Path, encoding: Optional[str], start: Optional[int], end: Optional[int]) -> str:
    # 以encoding读取全文本，然后切片；大文本可优化为分块按偏移读取（后续迭代）
    text = path.read_text(encoding=encoding or "utf-8", errors="ignore")
    s = start or 0
    e = end or len(text)
    return text[s:e]

