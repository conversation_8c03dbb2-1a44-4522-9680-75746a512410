import os
import time
from PIL import Image, ImageDraw, ImageFont
from html2image import Html2Image

class ImageGenerator:
    """统一图片生成器，支持多种渲染方式"""
    
    def __init__(self, output_dir=None):
        """
        初始化图片生成器
        
        Args:
            output_dir: 输出目录，默认为当前目录
        """
        self.output_dir = output_dir or os.path.dirname(os.path.abspath(__file__))
    
    def generate_from_html(self, html_file, output_file, size=(1300, 1100), css_selector=".promo-container"):
        """
        从HTML文件生成图片
        
        Args:
            html_file: HTML文件路径
            output_file: 输出图片路径
            size: 图片尺寸，默认(1300, 1100)
            css_selector: 要截图的元素选择器
        """
        # 获取HTML文件的绝对路径
        abs_html_path = os.path.abspath(html_file)
        file_url = f"file:///{abs_html_path}"
        
        # 输出文件名（不含路径）
        output_filename = os.path.basename(output_file)
        
        # 输出目录
        output_dir = os.path.dirname(os.path.abspath(output_file))
        
        # 创建Html2Image对象
        hti = Html2Image(output_path=output_dir)
        
        try:
            print(f"正在从HTML生成图片: {html_file} -> {output_file}")
            
            # 设置大小
            hti.size = size
            
            # 从文件生成图片
            hti.screenshot(
                url=file_url,
                save_as=output_filename,
                size=size
            )
            
            print(f"图片生成成功! 保存到: {output_file}")
            return True
        except Exception as e:
            print(f"生成图片时发生错误: {e}")
            return False
    
    def generate_from_scratch(self, output_file, content=None, size=(1200, 1000)):
        """
        使用PIL从零创建图片
        
        Args:
            output_file: 输出图片路径
            content: 图片内容配置
            size: 图片尺寸，默认(1200, 1000)
        """
        try:
            print(f"从零创建图片: {output_file}")
            
            # 默认内容配置
            default_content = {
                "title": "Obsidian 任务管理系统",
                "subtitle": "高效规划 · 科学执行 · 数据驱动",
                "description": "基于 Obsidian + Dataview 构建的全方位任务管理解决方案",
                "features": [
                    ("📊 任务统计卡片", "七彩卡片直观展示任务分布状态"),
                    ("🍅 番茄钟管理", "设定每日番茄钟目标，追踪实际完成情况"),
                    ("📁 项目任务分布", "智能识别项目文件和待整理任务"),
                    ("🎯 主子任务关系", "通过@任务名语法轻松关联主子任务"),
                    ("🔍 智能筛选", "多维度任务过滤系统，支持按条件组合筛选"),
                    ("📈 数据可视化", "实时生成关键指标的可视化图表")
                ],
                "workflow": ["快速捕获", "任务整理", "专注执行", "数据分析", "持续改进"],
                "footer": "Obsidian 任务管理系统 | 版本 v1.0 | 基于 Dataview 插件构建"
            }
            
            # 合并用户内容与默认内容
            if content:
                for key, value in content.items():
                    default_content[key] = value
            
            content = default_content
            width, height = size
            
            # 创建图片
            image = Image.new('RGB', (width, height), (26, 29, 41))
            draw = ImageDraw.Draw(image)
            
            # 尝试加载字体
            try:
                title_font = ImageFont.truetype("arial.ttf", 48)
                subtitle_font = ImageFont.truetype("arial.ttf", 24)
                normal_font = ImageFont.truetype("arial.ttf", 18)
                small_font = ImageFont.truetype("arial.ttf", 14)
            except IOError:
                title_font = ImageFont.load_default()
                subtitle_font = ImageFont.load_default()
                normal_font = ImageFont.load_default()
                small_font = ImageFont.load_default()
            
            # 绘制标题
            title = content["title"]
            title_width = draw.textlength(title, font=title_font)
            draw.text(((width - title_width) / 2, 50), title, font=title_font, fill=(0, 212, 170))
            
            # 绘制副标题
            subtitle = content["subtitle"]
            subtitle_width = draw.textlength(subtitle, font=subtitle_font)
            draw.text(((width - subtitle_width) / 2, 120), subtitle, font=subtitle_font, fill=(255, 255, 255))
            
            # 绘制描述
            description = content["description"]
            desc_width = draw.textlength(description, font=normal_font)
            draw.text(((width - desc_width) / 2, 170), description, font=normal_font, fill=(160, 163, 177))
            
            # 绘制功能模块
            features = content["features"]
            col_width = width // 3
            row_height = 180
            padding = 20
            
            for i, (title, desc) in enumerate(features):
                col = i % 3
                row = i // 3
                
                x = col * col_width + padding
                y = 250 + row * row_height
                
                # 绘制模块背景
                draw.rectangle(
                    [x, y, x + col_width - padding * 2, y + row_height - padding],
                    fill=(40, 43, 55),
                    outline=(0, 212, 170),
                    width=1
                )
                
                # 绘制模块标题
                draw.text((x + 15, y + 15), title, font=subtitle_font, fill=(0, 212, 170))
                
                # 绘制模块描述
                draw.text((x + 15, y + 60), desc, font=small_font, fill=(160, 163, 177))
            
            # 绘制工作流程
            workflow = content["workflow"]
            flow_y = 650
            flow_width = width - 100
            step_width = flow_width // len(workflow)
            
            for i, step in enumerate(workflow):
                x = 50 + i * step_width
                
                # 绘制步骤背景
                draw.rectangle(
                    [x, flow_y, x + step_width - 10, flow_y + 60],
                    fill=(40, 43, 55),
                    outline=(0, 212, 170),
                    width=1
                )
                
                # 绘制步骤文字
                step_width_text = draw.textlength(step, font=normal_font)
                draw.text((x + (step_width - 10 - step_width_text) / 2, flow_y + 20), step, font=normal_font, fill=(255, 255, 255))
                
                # 绘制箭头（除了最后一个步骤）
                if i < len(workflow) - 1:
                    draw.text((x + step_width - 5, flow_y + 20), "→", font=normal_font, fill=(0, 212, 170))
            
            # 绘制底部信息
            footer = content["footer"]
            footer_width = draw.textlength(footer, font=small_font)
            draw.text(((width - footer_width) / 2, height - 50), footer, font=small_font, fill=(139, 142, 155))
            
            # 保存图片
            image.save(output_file)
            print(f"图片生成成功! 保存到: {output_file}")
            return True
        except Exception as e:
            print(f"生成图片时发生错误: {e}")
            return False
    
    def generate(self, output_file, method="html", input_file=None, content=None, size=None):
        """
        统一的图片生成入口
        
        Args:
            output_file: 输出图片路径
            method: 生成方法，"html"或"pil"
            input_file: 输入HTML文件路径（当method为"html"时需要）
            content: 图片内容配置（当method为"pil"时使用）
            size: 图片尺寸
        """
        if method == "html":
            if not input_file:
                print("使用HTML方法生成图片时必须提供input_file参数")
                return False
            
            html_size = size or (1300, 1100)
            return self.generate_from_html(input_file, output_file, size=html_size)
        else:
            pil_size = size or (1200, 1000)
            return self.generate_from_scratch(output_file, content, size=pil_size)


# 使用示例
if __name__ == "__main__":
    # 创建图片生成器
    generator = ImageGenerator()
    
    # 设置文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 方法1: 使用HTML生成图片
    html_file = os.path.join(current_dir, "obsidian_task_hexagon_promo.html")
    html_output_file = os.path.join(current_dir, "unified_html_output.png")
    generator.generate(html_output_file, method="html", input_file=html_file)
    
    # 方法2: 使用PIL从零创建图片
    pil_output_file = os.path.join(current_dir, "unified_pil_output.png")
    
    # 自定义内容
    custom_content = {
        "title": "Obsidian 任务管理系统 2.0",
        "subtitle": "更高效 · 更智能 · 更直观",
        "workflow": ["捕获", "整理", "执行", "分析", "改进"]
    }
    
    generator.generate(pil_output_file, method="pil", content=custom_content) 