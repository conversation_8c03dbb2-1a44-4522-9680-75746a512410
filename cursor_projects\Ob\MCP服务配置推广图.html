<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP服务配置推广图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            width: 1200px;
            height: 1800px;
            overflow: hidden;
            position: relative;
        }
        
        .container {
            padding: 80px 60px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            text-align: center;
            margin-bottom: 80px;
        }
        
        .main-title {
            font-size: 72px;
            font-weight: bold;
            color: #00d4aa;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(0, 212, 170, 0.3);
        }
        
        .subtitle {
            font-size: 32px;
            color: #ffffff;
            margin-bottom: 15px;
            font-weight: 300;
        }
        
        .tagline {
            font-size: 24px;
            color: #a0a0a0;
            font-weight: 300;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 40px;
            flex: 1;
            margin-bottom: 60px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }
        
        .feature-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #00d4aa;
        }
        
        .feature-desc {
            font-size: 18px;
            line-height: 1.6;
            color: #e0e0e0;
        }
        
        .workflow {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 60px;
            margin-top: auto;
        }
        
        .workflow-item {
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .workflow-icon {
            font-size: 36px;
            margin-bottom: 10px;
            width: 80px;
            height: 80px;
            background: rgba(0, 212, 170, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #00d4aa;
        }
        
        .workflow-text {
            font-size: 20px;
            color: #ffffff;
            font-weight: 500;
        }
        
        .workflow-arrow {
            font-size: 24px;
            color: #00d4aa;
            margin: 0 20px;
        }
        
        .card1 { background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.05)); }
        .card2 { background: linear-gradient(135deg, rgba(255, 87, 34, 0.2), rgba(255, 87, 34, 0.05)); }
        .card3 { background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.05)); }
        .card4 { background: linear-gradient(135deg, rgba(233, 30, 99, 0.2), rgba(233, 30, 99, 0.05)); }
        .card5 { background: linear-gradient(135deg, rgba(63, 81, 181, 0.2), rgba(63, 81, 181, 0.05)); }
        .card6 { background: linear-gradient(135deg, rgba(156, 39, 176, 0.2), rgba(156, 39, 176, 0.05)); }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">MCP服务配置指南</h1>
            <p class="subtitle">9个核心服务 · 标准化配置 · 一键部署</p>
            <p class="tagline">基于实际测试的完整MCP服务配置解决方案</p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card card1">
                <span class="feature-icon">📝</span>
                <h3 class="feature-title">交互反馈系统</h3>
                <p class="feature-desc">mcp-feedback-enhanced提供用户交互反馈和系统信息获取，支持环境诊断和性能监控。</p>
            </div>
            
            <div class="feature-card card2">
                <span class="feature-icon">📚</span>
                <h3 class="feature-title">知识库集成</h3>
                <p class="feature-desc">mcp-obsidian实现Obsidian知识库操作，支持笔记读取、搜索和创建，打通知识管理流程。</p>
            </div>
            
            <div class="feature-card card3">
                <span class="feature-icon">🧠</span>
                <h3 class="feature-title">智能思维链</h3>
                <p class="feature-desc">sequential-thinking提供结构化思维分析，支持复杂问题分解和逐步推理。</p>
            </div>
            
            <div class="feature-card card4">
                <span class="feature-icon">🌐</span>
                <h3 class="feature-title">浏览器自动化</h3>
                <p class="feature-desc">playwright实现网页自动化操作，支持截图、表单填写和数据抓取等功能。</p>
            </div>
            
            <div class="feature-card card5">
                <span class="feature-icon">🎨</span>
                <h3 class="feature-title">AI图像生成</h3>
                <p class="feature-desc">replicate-flux和together-image-gen提供高质量AI图像生成，支持多种风格和格式。</p>
            </div>
            
            <div class="feature-card card6">
                <span class="feature-icon">📋</span>
                <h3 class="feature-title">任务管理系统</h3>
                <p class="feature-desc">shrimp-task-manager提供智能任务规划和执行，支持项目管理和工作流程优化。</p>
            </div>
        </div>
        
        <div class="workflow">
            <div class="workflow-item">
                <div class="workflow-icon">⚙️</div>
                <span class="workflow-text">配置</span>
            </div>
            <span class="workflow-arrow">→</span>
            <div class="workflow-item">
                <div class="workflow-icon">🔗</div>
                <span class="workflow-text">集成</span>
            </div>
            <span class="workflow-arrow">→</span>
            <div class="workflow-item">
                <div class="workflow-icon">🚀</div>
                <span class="workflow-text">部署</span>
            </div>
            <span class="workflow-arrow">→</span>
            <div class="workflow-item">
                <div class="workflow-icon">📊</div>
                <span class="workflow-text">监控</span>
            </div>
        </div>
    </div>
</body>
</html>
