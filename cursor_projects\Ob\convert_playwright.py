#!/usr/bin/env python3
"""
使用Playwright将HTML海报转换为高质量JPG图片
"""

import asyncio
import os
from playwright.async_api import async_playwright

async def html_to_jpg(html_file_path, output_jpg_path, width=1200, height=1800, quality=95):
    """
    将HTML文件转换为JPG图片
    
    Args:
        html_file_path: HTML文件路径
        output_jpg_path: 输出JPG文件路径
        width: 图片宽度
        height: 图片高度
        quality: JPG质量 (1-100)
    """
    
    async with async_playwright() as p:
        try:
            # 启动浏览器
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            # 设置视口大小
            await page.set_viewport_size({"width": width, "height": height})
            
            # 打开HTML文件
            file_url = f"file:///{os.path.abspath(html_file_path).replace(os.sep, '/')}"
            await page.goto(file_url)
            
            # 等待页面加载完成
            await page.wait_for_load_state('networkidle')
            await asyncio.sleep(2)  # 额外等待确保渲染完成
            
            # 截图并保存为JPG
            await page.screenshot(
                path=output_jpg_path,
                type='jpeg',
                quality=quality,
                full_page=True
            )
            
            await browser.close()
            
            print(f"✅ 成功生成JPG图片: {output_jpg_path}")
            print(f"📏 图片尺寸: {width} x {height} 像素")
            print(f"🎨 图片质量: {quality}%")
            
            return True
            
        except Exception as e:
            print(f"❌ 转换失败: {str(e)}")
            return False

async def main():
    # 文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    html_file = os.path.join(current_dir, "MCP配置报告推广海报.html")
    jpg_file = os.path.join(current_dir, "MCP配置报告推广海报.jpg")
    
    # 检查HTML文件是否存在
    if not os.path.exists(html_file):
        print(f"❌ HTML文件不存在: {html_file}")
        return False
    
    print("🚀 开始转换HTML到JPG...")
    print(f"📁 输入文件: {html_file}")
    print(f"📁 输出文件: {jpg_file}")
    
    # 执行转换
    success = await html_to_jpg(html_file, jpg_file, width=1200, height=1800, quality=95)
    
    if success:
        print("\n🎉 转换完成！")
        print(f"📂 JPG文件已保存到: {jpg_file}")
        
        # 检查文件大小
        if os.path.exists(jpg_file):
            file_size = os.path.getsize(jpg_file) / 1024 / 1024  # MB
            print(f"📊 文件大小: {file_size:.2f} MB")
    else:
        print("\n💥 转换失败，请检查错误信息")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
