@echo off
chcp 65001 >nul
echo ========================================
echo        Python脚本打包演示
echo ========================================
echo.

echo 1. 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo.
echo 2. 安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖包安装失败
    pause
    exit /b 1
)

echo.
echo 3. 清理之前的构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del /q "*.spec"

echo.
echo 4. 开始打包...
echo 正在生成单文件可执行程序...
pyinstaller --onefile --console --name "SystemInfoViewer" demo_script.py

if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo ✅ 打包完成！
echo 可执行文件位置: dist\SystemInfoViewer.exe
echo.

echo 5. 测试打包结果...
if exist "dist\SystemInfoViewer.exe" (
    echo ✅ 可执行文件生成成功
    echo 文件大小: 
    for %%i in ("dist\SystemInfoViewer.exe") do echo    %%~zi 字节 ^(%%~zi/1024/1024 MB^)
) else (
    echo ❌ 可执行文件未找到
)

echo.
echo ========================================
echo 打包完成！按任意键退出...
pause >nul
