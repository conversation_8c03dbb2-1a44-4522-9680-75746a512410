#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速生成AI风格JPG - 使用简单方法
"""

import os
import subprocess
import webbrowser
import time

def open_html_files():
    """打开所有HTML文件供手动截图"""
    html_files = [
        "MCP配置报告推广海报.html"
    ]

    print("🌐 正在打开HTML文件...")
    for html_file in html_files:
        if os.path.exists(html_file):
            file_path = os.path.abspath(html_file)
            file_url = f"file:///{file_path.replace(os.sep, '/')}"
            webbrowser.open(file_url)
            print(f"✅ 已打开: {html_file}")
            time.sleep(1)  # 避免同时打开太多
        else:
            print(f"❌ 文件不存在: {html_file}")

def check_existing_files():
    """检查现有文件"""
    print("📁 检查现有文件:")

    html_files = [f for f in os.listdir('.') if f.endswith('.html') and 'MCP配置报告' in f]
    jpg_files = [f for f in os.listdir('.') if f.endswith('.jpg') and 'MCP配置报告' in f]

    print(f"\n✅ HTML文件 ({len(html_files)}个):")
    for f in html_files:
        print(f"  - {f}")

    print(f"\n📸 JPG文件 ({len(jpg_files)}个):")
    for f in jpg_files:
        size = os.path.getsize(f) / 1024  # KB
        print(f"  - {f} ({size:.1f} KB)")

    missing_jpg = []
    for html_file in html_files:
        jpg_file = html_file.replace('.html', '.jpg')
        if jpg_file not in jpg_files:
            missing_jpg.append(jpg_file)

    if missing_jpg:
        print(f"\n❌ 缺失的JPG文件 ({len(missing_jpg)}个):")
        for f in missing_jpg:
            print(f"  - {f}")
    else:
        print("\n🎉 所有JPG文件都已存在！")

def main():
    """主函数"""
    print("🎨 MCP配置报告推广海报JPG生成助手")
    print("=" * 50)

    # 检查现有文件
    check_existing_files()

    print("\n" + "=" * 50)
    print("📸 手动截图指南:")
    print("1. 浏览器中按 F12 打开开发者工具")
    print("2. 按 Ctrl+Shift+P 打开命令面板")
    print("3. 输入 'screenshot' 并选择 'Capture full size screenshot'")
    print("4. 图片会自动下载到默认下载文件夹")
    print("5. 将图片重命名并移动到当前目录")

    print(f"\n📁 当前目录: {os.getcwd()}")
    print("\n🌐 正在为您打开HTML文件...")

    # 打开HTML文件
    open_html_files()

    print("\n✨ MCP配置报告推广海报已在浏览器中打开！")
    print("💡 请按照指南进行截图，保存为：MCP配置报告推广海报.jpg")

if __name__ == "__main__":
    main()
