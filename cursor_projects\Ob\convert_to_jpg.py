#!/usr/bin/env python3
"""
将HTML海报转换为高质量JPG图片
"""

import os
import sys
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
from PIL import Image
import io

def html_to_jpg(html_file_path, output_jpg_path, width=1200, height=1800, quality=95):
    """
    将HTML文件转换为JPG图片
    
    Args:
        html_file_path: HTML文件路径
        output_jpg_path: 输出JPG文件路径
        width: 图片宽度
        height: 图片高度
        quality: JPG质量 (1-100)
    """
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument(f'--window-size={width},{height}')
    chrome_options.add_argument('--force-device-scale-factor=2')  # 高DPI
    
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        
        # 设置窗口大小
        driver.set_window_size(width, height)
        
        # 打开HTML文件
        file_url = f"file:///{os.path.abspath(html_file_path).replace(os.sep, '/')}"
        driver.get(file_url)
        
        # 等待页面加载完成
        time.sleep(3)
        
        # 截图
        screenshot = driver.get_screenshot_as_png()
        
        # 使用PIL处理图片
        image = Image.open(io.BytesIO(screenshot))
        
        # 转换为RGB模式（JPG不支持透明度）
        if image.mode in ('RGBA', 'LA'):
            # 创建白色背景
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'RGBA':
                background.paste(image, mask=image.split()[-1])  # 使用alpha通道作为mask
            else:
                background.paste(image)
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 保存为JPG
        image.save(output_jpg_path, 'JPEG', quality=quality, optimize=True)
        
        print(f"✅ 成功生成JPG图片: {output_jpg_path}")
        print(f"📏 图片尺寸: {image.size[0]} x {image.size[1]} 像素")
        print(f"🎨 图片质量: {quality}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        return False
        
    finally:
        try:
            driver.quit()
        except:
            pass

def main():
    # 文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    html_file = os.path.join(current_dir, "MCP配置报告推广海报.html")
    jpg_file = os.path.join(current_dir, "MCP配置报告推广海报.jpg")
    
    # 检查HTML文件是否存在
    if not os.path.exists(html_file):
        print(f"❌ HTML文件不存在: {html_file}")
        return False
    
    print("🚀 开始转换HTML到JPG...")
    print(f"📁 输入文件: {html_file}")
    print(f"📁 输出文件: {jpg_file}")
    
    # 执行转换
    success = html_to_jpg(html_file, jpg_file, width=1200, height=1800, quality=95)
    
    if success:
        print("\n🎉 转换完成！")
        print(f"📂 JPG文件已保存到: {jpg_file}")
    else:
        print("\n💥 转换失败，请检查错误信息")
    
    return success

if __name__ == "__main__":
    main()
