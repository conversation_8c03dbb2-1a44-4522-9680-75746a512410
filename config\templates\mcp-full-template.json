{"mcpServers": {"mcp-obsidian": {"command": "uvx", "args": ["mcp-obsidian"], "env": {"OBSIDIAN_API_KEY": "YOUR_OBSIDIAN_API_KEY_HERE", "OBSIDIAN_HOST": "https://127.0.0.1:27124"}}, "shrimp-task-manager": {"command": "uvx", "args": ["shrimp-task-manager"], "env": {}}, "replicate-flux-mcp": {"command": "uvx", "args": ["replicate-flux-mcp"], "env": {"REPLICATE_API_TOKEN": "YOUR_REPLICATE_API_TOKEN_HERE"}}}}