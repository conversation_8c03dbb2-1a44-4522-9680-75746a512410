# 🛠️ 开发指南

> 测试库项目开发者完整指南，包含架构设计、开发规范、调试技巧等详细信息。

## 📋 目录

- [🏗️ 项目架构](#️-项目架构)
- [🔧 开发环境](#-开发环境)
- [📝 编码规范](#-编码规范)
- [🧪 测试策略](#-测试策略)
- [🚀 部署流程](#-部署流程)
- [🔍 调试技巧](#-调试技巧)
- [📊 性能优化](#-性能优化)
- [🔒 安全考虑](#-安全考虑)

## 🏗️ 项目架构

### 📁 目录结构

项目采用标准化的开源项目结构，经过2025年6月的结构改造，现在的组织方式如下：

```
测试库/
├── obsidian-vault/             # Obsidian知识库（迁移后）
│   ├── 0_Bullet Journal/      # 日记系统
│   ├── 1_Fleeting notes/      # 临时笔记
│   ├── 2_Literature notes/    # 文献笔记
│   ├── 3_Permanent notes/     # 永久笔记
│   ├── 4_References/          # 参考资料
│   ├── 5_Structures/          # 结构化内容
│   ├── 6_Project Notes/       # 项目笔记
│   ├── 7_Task Notes/          # 任务笔记
│   ├── Templates/             # 模板文件
│   └── .obsidian/            # Obsidian配置
├── tools/                      # 开发工具集
│   ├── content-generator/      # 内容生成工具
│   ├── data-processor/         # 数据处理工具
│   ├── mcp-tools/             # MCP相关工具
│   └── web-tools/             # Web开发工具
├── scripts/                    # 脚本文件
│   ├── install/               # 安装脚本
│   └── maintenance/           # 维护脚本
├── config/                     # 配置文件
│   ├── mcp/                   # MCP配置
│   │   ├── cursor/            # Cursor IDE配置
│   │   └── augment/           # Augment IDE配置
│   └── templates/             # 配置模板
├── output/                     # 输出文件
│   ├── images/                # 图片输出
│   │   ├── promotional/       # 推广图片
│   │   ├── screenshots/       # 截图文件
│   │   └── generated/         # 生成图片
│   ├── exports/               # 导出文件
│   └── reports/               # 报告文件
├── temp/                       # 临时文件
│   ├── cache/                 # 缓存文件
│   ├── logs/                  # 日志文件
│   └── backup/                # 备份文件
├── docs/                       # 项目文档
├── tests/                      # 测试文件
├── package.json               # Node.js配置
├── requirements.txt           # Python依赖
├── .env                       # 环境变量
├── .gitignore                # Git忽略规则
└── README.md                  # 项目说明
```

### 整体架构

```mermaid
graph TB
    subgraph "用户界面层"
        UI[Obsidian界面]
        WEB[Web界面]
        CLI[命令行工具]
    end

    subgraph "业务逻辑层"
        KM[知识管理]
        TM[任务管理]
        CG[内容生成]
        DM[数据管理]
    end

    subgraph "服务集成层"
        MCP[MCP协议]
        API[REST API]
        AI[AI服务]
    end

    subgraph "数据存储层"
        MD[Markdown文件]
        DB[SQLite数据库]
        FS[文件系统]
        CFG[配置文件]
    end

    UI --> KM
    WEB --> TM
    CLI --> CG
    KM --> MCP
    TM --> API
    CG --> AI
    MCP --> MD
    API --> DB
    AI --> FS
    DM --> CFG
```

### 模块设计

#### 核心模块

**知识管理模块 (Knowledge Management)**
```python
# tools/knowledge_manager/
├── __init__.py
├── obsidian_client.py      # Obsidian API客户端
├── dataview_processor.py   # Dataview查询处理
├── content_indexer.py      # 内容索引器
└── link_analyzer.py        # 链接关系分析
```

**任务管理模块 (Task Management)**
```python
# tools/task_manager/
├── __init__.py
├── task_processor.py       # 任务处理器
├── project_tracker.py      # 项目追踪器
├── time_manager.py         # 时间管理
└── progress_calculator.py  # 进度计算器
```

**内容生成模块 (Content Generation)**
```python
# tools/content_generator/
├── __init__.py
├── promo_generator.py      # 推广图生成器
├── template_engine.py      # 模板引擎
├── image_processor.py      # 图像处理器
└── batch_processor.py      # 批量处理器
```

#### 支持模块

**MCP集成模块**
```python
# tools/mcp_integration/
├── __init__.py
├── mcp_client.py           # MCP客户端
├── tool_manager.py         # 工具管理器
├── config_manager.py       # 配置管理器
└── diagnostics.py          # 诊断工具
```

**数据处理模块**
```python
# tools/data_processor/
├── __init__.py
├── chat_extractor.py       # 聊天记录提取
├── data_exporter.py        # 数据导出器
├── format_converter.py     # 格式转换器
└── validator.py            # 数据验证器
```

### 设计模式

#### 工厂模式
```python
class GeneratorFactory:
    """内容生成器工厂"""

    @staticmethod
    def create_generator(generator_type: str):
        generators = {
            'promo': PromoGenerator,
            'infographic': InfographicGenerator,
            'batch': BatchGenerator
        }

        if generator_type not in generators:
            raise ValueError(f"Unknown generator type: {generator_type}")

        return generators[generator_type]()
```

#### 策略模式
```python
class ImageProcessor:
    """图像处理器"""

    def __init__(self, strategy: ImageProcessingStrategy):
        self.strategy = strategy

    def process(self, image_data: bytes) -> bytes:
        return self.strategy.process(image_data)

class PlaywrightStrategy(ImageProcessingStrategy):
    """Playwright截图策略"""

    def process(self, html_content: str) -> bytes:
        # Playwright实现
        pass

class Html2ImageStrategy(ImageProcessingStrategy):
    """Html2Image策略"""

    def process(self, html_content: str) -> bytes:
        # Html2Image实现
        pass
```

#### 观察者模式
```python
class TaskManager:
    """任务管理器"""

    def __init__(self):
        self.observers = []

    def add_observer(self, observer):
        self.observers.append(observer)

    def notify_observers(self, event):
        for observer in self.observers:
            observer.update(event)

    def complete_task(self, task_id):
        # 完成任务逻辑
        self.notify_observers(TaskCompletedEvent(task_id))
```

## 🔧 开发环境

### 环境配置

#### Python环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
source venv/bin/activate  # Linux/macOS
venv\Scripts\activate     # Windows

# 安装开发依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

#### Node.js环境
```bash
# 安装Node.js依赖
npm install

# 安装开发工具
npm install -g eslint prettier nodemon
```

#### 开发工具配置

**VS Code配置 (.vscode/settings.json)**
```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    },
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/node_modules": true,
        "**/.pytest_cache": true
    }
}
```

**PyCharm配置**
- 设置Python解释器为虚拟环境
- 启用代码检查和格式化
- 配置测试运行器为pytest
- 设置代码模板和Live Templates

### 调试配置

#### Python调试
```python
# 使用logging进行调试
import logging

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def debug_function():
    logger.debug("调试信息")
    logger.info("一般信息")
    logger.warning("警告信息")
    logger.error("错误信息")
```

#### 断点调试
```python
# 使用pdb进行断点调试
import pdb

def complex_function(data):
    # 设置断点
    pdb.set_trace()

    # 处理数据
    result = process_data(data)
    return result

# 使用ipdb（增强版pdb）
import ipdb
ipdb.set_trace()
```

## 📝 编码规范

### Python编码规范

#### 代码结构
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块文档字符串
描述模块的功能和用途
"""

import os
import sys
from typing import Dict, List, Optional, Union
from pathlib import Path

# 第三方库导入
import requests
import pandas as pd

# 本地模块导入
from tools.base import BaseProcessor
from utils.logger import get_logger

# 模块级常量
DEFAULT_TIMEOUT = 30
MAX_RETRY_COUNT = 3

logger = get_logger(__name__)


class ContentProcessor(BaseProcessor):
    """
    内容处理器

    负责处理各种类型的内容，包括文本、图片、视频等。

    Attributes:
        config (Dict): 配置信息
        timeout (int): 超时时间

    Example:
        >>> processor = ContentProcessor(config={'timeout': 30})
        >>> result = processor.process_content(content_data)
        >>> print(result)
    """

    def __init__(self, config: Dict[str, any] = None):
        """
        初始化内容处理器

        Args:
            config (Dict[str, any], optional): 配置信息. Defaults to None.
        """
        super().__init__()
        self.config = config or {}
        self.timeout = self.config.get('timeout', DEFAULT_TIMEOUT)

    def process_content(self, content: str) -> Optional[Dict[str, any]]:
        """
        处理内容

        Args:
            content (str): 待处理的内容

        Returns:
            Optional[Dict[str, any]]: 处理结果，失败时返回None

        Raises:
            ValueError: 当内容格式不正确时
            ProcessingError: 当处理过程出错时
        """
        try:
            # 验证输入
            if not content or not isinstance(content, str):
                raise ValueError("内容不能为空且必须是字符串")

            # 处理逻辑
            result = self._internal_process(content)

            logger.info(f"内容处理成功，长度: {len(content)}")
            return result

        except Exception as e:
            logger.error(f"内容处理失败: {e}")
            return None

    def _internal_process(self, content: str) -> Dict[str, any]:
        """内部处理方法"""
        # 具体实现
        pass


def utility_function(param1: str, param2: int = 10) -> bool:
    """
    工具函数示例

    Args:
        param1 (str): 参数1描述
        param2 (int, optional): 参数2描述. Defaults to 10.

    Returns:
        bool: 处理结果
    """
    return True


if __name__ == "__main__":
    # 模块测试代码
    processor = ContentProcessor()
    result = processor.process_content("测试内容")
    print(result)
```

#### 错误处理
```python
# 自定义异常
class ProcessingError(Exception):
    """处理过程中的错误"""
    pass

class ConfigurationError(Exception):
    """配置错误"""
    pass

# 错误处理最佳实践
def safe_api_call(url: str, timeout: int = 30) -> Optional[Dict]:
    """安全的API调用"""
    try:
        response = requests.get(url, timeout=timeout)
        response.raise_for_status()
        return response.json()

    except requests.exceptions.Timeout:
        logger.error(f"API调用超时: {url}")
        return None

    except requests.exceptions.HTTPError as e:
        logger.error(f"HTTP错误: {e.response.status_code} - {url}")
        return None

    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常: {e}")
        return None

    except ValueError as e:
        logger.error(f"JSON解析错误: {e}")
        return None

    except Exception as e:
        logger.error(f"未知错误: {e}")
        return None
```

### JavaScript编码规范

#### 基础规范
```javascript
// 使用严格模式
'use strict';

// 导入模块
const fs = require('fs').promises;
const path = require('path');
const { promisify } = require('util');

// 常量定义
const DEFAULT_TIMEOUT = 30000;
const MAX_RETRY_COUNT = 3;

/**
 * 内容处理器类
 * @class ContentProcessor
 */
class ContentProcessor {
    /**
     * 构造函数
     * @param {Object} config - 配置对象
     * @param {number} config.timeout - 超时时间
     */
    constructor(config = {}) {
        this.config = config;
        this.timeout = config.timeout || DEFAULT_TIMEOUT;
    }

    /**
     * 处理内容
     * @param {string} content - 待处理内容
     * @returns {Promise<Object|null>} 处理结果
     */
    async processContent(content) {
        try {
            // 输入验证
            if (!content || typeof content !== 'string') {
                throw new Error('内容不能为空且必须是字符串');
            }

            // 处理逻辑
            const result = await this._internalProcess(content);

            console.log(`内容处理成功，长度: ${content.length}`);
            return result;

        } catch (error) {
            console.error(`内容处理失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 内部处理方法
     * @private
     * @param {string} content - 内容
     * @returns {Promise<Object>} 处理结果
     */
    async _internalProcess(content) {
        // 具体实现
        return { processed: true, content };
    }
}

/**
 * 工具函数示例
 * @param {string} param1 - 参数1
 * @param {number} param2 - 参数2
 * @returns {boolean} 处理结果
 */
function utilityFunction(param1, param2 = 10) {
    return true;
}

// 导出模块
module.exports = {
    ContentProcessor,
    utilityFunction
};
```

## 🧪 测试策略

### 测试金字塔

```
      /\
     /  \     E2E测试 (5%)
    /____\
   /      \   集成测试 (15%)
  /________\  单元测试 (80%)
```

### 单元测试

#### 测试结构
```python
# tests/test_content_processor.py
import pytest
from unittest.mock import Mock, patch, MagicMock
from tools.content_processor import ContentProcessor, ProcessingError

class TestContentProcessor:
    """内容处理器测试类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.config = {'timeout': 30}
        self.processor = ContentProcessor(self.config)

    def teardown_method(self):
        """每个测试方法后的清理"""
        # 清理资源
        pass

    def test_init_with_config(self):
        """测试带配置的初始化"""
        processor = ContentProcessor({'timeout': 60})
        assert processor.timeout == 60

    def test_init_without_config(self):
        """测试无配置的初始化"""
        processor = ContentProcessor()
        assert processor.timeout == 30  # 默认值

    def test_process_content_success(self):
        """测试内容处理成功场景"""
        content = "测试内容"

        result = self.processor.process_content(content)

        assert result is not None
        assert result['processed'] is True

    def test_process_content_empty_input(self):
        """测试空输入场景"""
        result = self.processor.process_content("")

        assert result is None

    def test_process_content_invalid_type(self):
        """测试无效类型输入"""
        result = self.processor.process_content(123)

        assert result is None

    @patch('tools.content_processor.requests.get')
    def test_api_call_timeout(self, mock_get):
        """测试API调用超时"""
        mock_get.side_effect = requests.exceptions.Timeout()

        result = self.processor.call_external_api("http://example.com")

        assert result is None

    @pytest.mark.parametrize("input_data,expected", [
        ("短文本", True),
        ("中等长度的文本内容", True),
        ("很长很长很长的文本内容" * 100, True),
    ])
    def test_process_various_lengths(self, input_data, expected):
        """测试不同长度的输入"""
        result = self.processor.process_content(input_data)

        assert (result is not None) == expected
```

### 集成测试

```python
# tests/integration/test_mcp_integration.py
import pytest
from tools.mcp_integration import MCPClient
from tools.obsidian_client import ObsidianClient

class TestMCPIntegration:
    """MCP集成测试"""

    @pytest.fixture(scope="class")
    def mcp_client(self):
        """MCP客户端fixture"""
        client = MCPClient()
        yield client
        client.close()

    @pytest.fixture(scope="class")
    def obsidian_client(self):
        """Obsidian客户端fixture"""
        client = ObsidianClient()
        yield client
        client.close()

    def test_mcp_obsidian_list_files(self, mcp_client, obsidian_client):
        """测试MCP Obsidian文件列表功能"""
        # 通过MCP调用
        mcp_files = mcp_client.list_vault_files()

        # 直接调用Obsidian API
        direct_files = obsidian_client.list_files()

        # 验证结果一致性
        assert len(mcp_files) == len(direct_files)
        assert set(mcp_files) == set(direct_files)
```

### 端到端测试

```python
# tests/e2e/test_full_workflow.py
import pytest
from playwright.sync_api import sync_playwright

class TestFullWorkflow:
    """端到端测试"""

    def test_promotional_image_generation_workflow(self):
        """测试推广图生成完整流程"""
        # 1. 准备测试数据
        content_config = {
            "title": "测试产品",
            "description": "这是一个测试产品",
            "features": ["特性1", "特性2", "特性3"]
        }

        # 2. 生成推广图
        from tools.content_generator import PromoGenerator
        generator = PromoGenerator()

        result = generator.generate(content_config, style="bento")

        # 3. 验证结果
        assert result.success is True
        assert result.output_path.exists()
        assert result.output_path.stat().st_size > 0

        # 4. 验证图片质量
        from PIL import Image
        img = Image.open(result.output_path)
        assert img.size[0] >= 1920  # 宽度
        assert img.size[1] >= 1080  # 高度
```

## 🚀 部署流程

### 本地部署

```bash
# 1. 环境准备
python scripts/install-dependencies.py

# 2. 配置设置
cp config/templates/.env.template .env
# 编辑 .env 文件

# 3. 数据库初始化
python scripts/init-database.py

# 4. 启动服务
npm run start
```

### 生产部署

```bash
# 1. 构建项目
npm run build

# 2. 环境配置
export NODE_ENV=production
export PYTHON_ENV=production

# 3. 数据库迁移
python scripts/migrate-database.py

# 4. 启动生产服务
npm run start:prod
```

### Docker部署

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 安装Playwright浏览器
RUN playwright install

# 复制项目文件
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app
ENV NODE_ENV=production

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["python", "scripts/start-services.py"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PYTHON_ENV=production
    volumes:
      - ./data:/app/data
      - ./output:/app/output
    depends_on:
      - redis

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
```

---

*让知识管理变得简单而美好 🌸*
