import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import Chrome<PERSON>riverManager

def generate_promo_image(html_file, output_file):
    """
    使用Selenium将HTML文件转换为PNG图片
    
    Args:
        html_file: HTML文件路径
        output_file: 输出PNG文件路径
    """
    # 获取HTML文件的绝对路径
    abs_html_path = os.path.abspath(html_file)
    file_url = f"file:///{abs_html_path}"
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--window-size=1400,1050")  # 窗口大小略大于内容
    
    # 使用webdriver_manager自动下载和管理ChromeDriver
    print("初始化WebDriver...")
    driver = webdriver.Chrome(ChromeDriverManager().install(), options=chrome_options)
    
    try:
        # 加载HTML文件
        print(f"加载HTML文件: {file_url}")
        driver.get(file_url)
        
        # 等待页面加载完成
        time.sleep(2)
        
        # 找到需要截图的元素（在这里是整个推广容器）
        promo_element = driver.find_element_by_css_selector(".promo-container")
        
        # 截取元素图片并保存
        print(f"截图并保存到: {output_file}")
        promo_element.screenshot(output_file)
        
        print("图片生成成功!")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 关闭WebDriver
        driver.quit()

if __name__ == "__main__":
    # 设置文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    html_file = os.path.join(current_dir, "obsidian_task_hexagon_promo.html")
    output_file = os.path.join(current_dir, "obsidian_task_hexagon_promo.png")
    
    # 生成推广图片
    generate_promo_image(html_file, output_file) 