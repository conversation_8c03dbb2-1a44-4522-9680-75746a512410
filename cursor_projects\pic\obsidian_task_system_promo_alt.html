<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obsidian任务管理系统推广图片 - 备选版本</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .promo-container {
            width: 1200px;
            height: 1000px;
            position: relative;
            overflow: hidden;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, #12161F 0%, #202B3E 100%);
            color: white;
        }
        
        .header {
            position: relative;
            text-align: center;
            padding: 50px 0 30px;
            background: linear-gradient(135deg, rgba(100, 90, 172, 0.7) 0%, rgba(37, 116, 169, 0.7) 100%);
            border-bottom-left-radius: 50% 20%;
            border-bottom-right-radius: 50% 20%;
            margin-bottom: 30px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .main-title {
            font-size: 44px;
            font-weight: bold;
            color: white;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .subtitle {
            font-size: 22px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 20px;
        }
        
        .description {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .dashboard-preview {
            position: absolute;
            bottom: -60px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 160px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            z-index: 2;
        }
        
        .dashboard-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 20px;
            background: linear-gradient(90deg, #6190E8, #A7BFE8);
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }
        
        .dashboard-preview::after {
            content: '';
            position: absolute;
            top: 30px;
            left: 10px;
            width: 280px;
            height: 140px;
            background: linear-gradient(180deg, 
                rgba(255,255,255,0.05) 0%,
                rgba(255,255,255,0.05) 20%,
                rgba(30,144,255,0.1) 20%,
                rgba(30,144,255,0.1) 25%,
                rgba(255,255,255,0.05) 25%,
                rgba(255,255,255,0.05) 45%,
                rgba(75,181,67,0.1) 45%,
                rgba(75,181,67,0.1) 50%,
                rgba(255,255,255,0.05) 50%,
                rgba(255,255,255,0.05) 70%,
                rgba(255,69,0,0.1) 70%,
                rgba(255,69,0,0.1) 75%,
                rgba(255,255,255,0.05) 75%,
                rgba(255,255,255,0.05) 95%,
                rgba(138,43,226,0.1) 95%,
                rgba(138,43,226,0.1) 100%
            );
        }
        
        .features-section {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 25px;
            padding: 0 40px;
            margin-top: 30px;
        }
        
        .feature-card {
            position: relative;
            padding: 25px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 16px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            height: 220px;
            overflow: hidden;
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, transparent, currentColor, transparent);
            opacity: 0.5;
        }
        
        .card-1::before { color: #FF6B6B; }
        .card-2::before { color: #4ECDC4; }
        .card-3::before { color: #FFD166; }
        .card-4::before { color: #118AB2; }
        .card-5::before { color: #06D6A0; }
        .card-6::before { color: #6F60EA; }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            background: rgba(255, 255, 255, 0.05);
        }
        
        .feature-icon {
            font-size: 36px;
            margin-bottom: 15px;
        }
        
        .card-1 .feature-icon { color: #FF6B6B; }
        .card-2 .feature-icon { color: #4ECDC4; }
        .card-3 .feature-icon { color: #FFD166; }
        .card-4 .feature-icon { color: #118AB2; }
        .card-5 .feature-icon { color: #06D6A0; }
        .card-6 .feature-icon { color: #6F60EA; }
        
        .feature-title {
            font-size: 20px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 10px;
            position: relative;
        }
        
        .feature-desc {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
        }
        
        .workflow {
            margin: 40px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 20px;
            padding: 20px;
            position: relative;
        }
        
        .workflow::before {
            content: '';
            position: absolute;
            width: 80%;
            height: 2px;
            background: linear-gradient(90deg, 
                transparent 0%, 
                rgba(255,255,255,0.2) 15%, 
                rgba(255,255,255,0.2) 85%, 
                transparent 100%);
            top: 50%;
            left: 10%;
        }
        
        .workflow-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 1;
        }
        
        .workflow-step {
            text-align: center;
            width: 120px;
            position: relative;
        }
        
        .step-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.05);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .step-1 .step-icon { color: #FF6B6B; background: rgba(255, 107, 107, 0.1); }
        .step-2 .step-icon { color: #4ECDC4; background: rgba(78, 205, 196, 0.1); }
        .step-3 .step-icon { color: #FFD166; background: rgba(255, 209, 102, 0.1); }
        .step-4 .step-icon { color: #6F60EA; background: rgba(111, 96, 234, 0.1); }
        
        .step-title {
            font-size: 18px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }
        
        .step-desc {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.5);
            background: rgba(0, 0, 0, 0.2);
            position: absolute;
            bottom: 0;
            width: 100%;
            z-index: 1;
        }
        
        /* 装饰元素 */
        .decoration {
            position: absolute;
            z-index: -1;
            opacity: 0.3;
        }
        
        .dec1 {
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(111, 96, 234, 0.5) 0%, transparent 70%);
            top: 10%;
            left: 5%;
            filter: blur(50px);
        }
        
        .dec2 {
            width: 250px;
            height: 250px;
            background: radial-gradient(circle, rgba(78, 205, 196, 0.5) 0%, transparent 70%);
            bottom: 20%;
            right: 5%;
            filter: blur(40px);
        }
    </style>
</head>
<body>
    <div class="promo-container">
        <!-- 装饰元素 -->
        <div class="decoration dec1"></div>
        <div class="decoration dec2"></div>
        
        <!-- 标题区域 -->
        <div class="header">
            <div class="main-title">Obsidian 任务管理系统</div>
            <div class="subtitle">高效规划 · 科学执行 · 数据驱动</div>
            <div class="description">基于 Obsidian + Dataview 构建的全方位任务管理解决方案，
                让您的工作流程更加清晰高效</div>
            <div class="dashboard-preview"></div>
        </div>
        
        <!-- 功能模块区域 -->
        <div class="features-section">
            <!-- 功能卡片1: 任务统计卡片 -->
            <div class="feature-card card-1">
                <div class="feature-icon">📊</div>
                <div class="feature-title">任务统计卡片</div>
                <div class="feature-desc">
                    七彩卡片直观展示任务分布状态，包括今天、明天、本周、未来及逾期任务，让您的工作重点一目了然。
                </div>
            </div>
            
            <!-- 功能卡片2: 番茄钟管理 -->
            <div class="feature-card card-2">
                <div class="feature-icon">🍅</div>
                <div class="feature-title">番茄钟管理</div>
                <div class="feature-desc">
                    设定每日番茄钟目标，追踪实际完成情况，系统自动计算完成率并生成统计分析，持续优化您的时间投入。
                </div>
            </div>
            
            <!-- 功能卡片3: 项目任务分布 -->
            <div class="feature-card card-3">
                <div class="feature-icon">📁</div>
                <div class="feature-title">项目任务分布</div>
                <div class="feature-desc">
                    智能识别项目文件和待整理任务，自动统计已规划、待整理和已完成任务，直观展示项目进度与状态。
                </div>
            </div>
            
            <!-- 功能卡片4: 主子任务关系 -->
            <div class="feature-card card-4">
                <div class="feature-icon">🎯</div>
                <div class="feature-title">主子任务关系</div>
                <div class="feature-desc">
                    通过 @任务名 语法轻松关联主子任务，构建清晰的任务结构树，让复杂任务拆分管理变得简单直观。
                </div>
            </div>
            
            <!-- 功能卡片5: 智能筛选 -->
            <div class="feature-card card-5">
                <div class="feature-icon">🔍</div>
                <div class="feature-title">智能筛选</div>
                <div class="feature-desc">
                    多维度任务过滤系统，支持按优先级、标签、项目和时间范围等条件组合筛选，快速定位需要关注的任务。
                </div>
            </div>
            
            <!-- 功能卡片6: 数据可视化 -->
            <div class="feature-card card-6">
                <div class="feature-icon">📈</div>
                <div class="feature-title">数据可视化</div>
                <div class="feature-desc">
                    自动生成完成率与时间分布图表，直观展示工作效率数据，帮助您发现时间管理模式，持续优化工作习惯。
                </div>
            </div>
        </div>
        
        <!-- 工作流程区域 -->
        <div class="workflow">
            <div class="workflow-container">
                <div class="workflow-step step-1">
                    <div class="step-icon">📝</div>
                    <div class="step-title">规划</div>
                    <div class="step-desc">设定目标与任务分解</div>
                </div>
                
                <div class="workflow-step step-2">
                    <div class="step-icon">🚀</div>
                    <div class="step-title">执行</div>
                    <div class="step-desc">专注完成与状态更新</div>
                </div>
                
                <div class="workflow-step step-3">
                    <div class="step-icon">📊</div>
                    <div class="step-title">追踪</div>
                    <div class="step-desc">进度监控与数据收集</div>
                </div>
                
                <div class="workflow-step step-4">
                    <div class="step-icon">🔄</div>
                    <div class="step-title">复盘</div>
                    <div class="step-desc">效率分析与持续优化</div>
                </div>
            </div>
        </div>
        
        <!-- 页脚信息 -->
        <div class="footer">
            基于 Obsidian v1.4.x 与 Dataview v0.5.x · 兼容移动端与桌面端 · 开源免费
        </div>
    </div>
</body>
</html> 